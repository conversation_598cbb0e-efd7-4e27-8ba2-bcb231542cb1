# 🏢 Customer & Material CRUD System Guide

## 🎯 Overview
The system now includes comprehensive Customer Relationship Management (CRM) and advanced Material Size management with full CRUD functionality. This provides professional-grade business management capabilities for print estimation workflows.

## 🏢 Customer Management System

### **Customer Overview**
Complete customer management with business details, financial tracking, and relationship management.

#### **Core Customer Data**
- **Company Information**: Name, company number, VAT number
- **Business Details**: Description, website, industry classification
- **Financial Management**: Credit limits, payment terms (immediate to 90 days)
- **Status Tracking**: Active, Inactive, Prospect
- **Notes**: Additional business information and history

#### **Payment Terms Options**
- **Immediate**: Payment on delivery
- **7 Days**: Net 7 payment terms
- **14 Days**: Net 14 payment terms  
- **30 Days**: Net 30 payment terms (default)
- **60 Days**: Net 60 payment terms
- **90 Days**: Net 90 payment terms

### **Customer Contacts (Multiple per Customer)**
Each customer can have unlimited contacts with role-based management.

#### **Contact Information**
- **Personal Details**: First name, last name, job title
- **Communication**: Email, phone, mobile numbers
- **Role Management**: Primary contact designation
- **Notification Preferences**: 
  - Receives quotes (default: yes)
  - Receives invoices (default: no)
- **Contact Notes**: Additional information per contact

#### **Primary Contact System**
- **Automatic Management**: Setting one contact as primary removes primary status from others
- **Business Logic**: Ensures only one primary contact per customer
- **Display Integration**: Primary contact shown in customer listings

### **Customer Addresses (Multiple per Customer)**
Flexible address management supporting different business needs.

#### **Address Types**
- **Billing**: Invoice and payment address
- **Delivery**: Shipping and delivery address
- **Both**: Combined billing and delivery address

#### **Address Features**
- **Custom Labels**: "Head Office", "Warehouse", "Branch Office"
- **Complete UK Format**: Address lines, city, county, postcode, country
- **Default Designation**: Per-type default addresses
- **Delivery Instructions**: Special delivery notes and requirements
- **Company Names**: Different company names per address if needed

### **Customer Resource Features**

#### **Advanced Form Design**
- **Sectioned Layout**: Organised into logical groups
  - Customer Information
  - Financial Information  
  - Contacts (with repeater)
  - Addresses (with repeater)
  - Notes
- **Smart Validation**: Required fields and data type validation
- **Dynamic Repeaters**: Add/remove contacts and addresses as needed
- **Helper Text**: Guidance for complex fields

#### **Comprehensive Table View**
- **Company Name**: Primary identifier with search
- **Status Badge**: Visual status indicators (Active/Prospect/Inactive)
- **Primary Contact**: Shows main contact name and email
- **Payment Terms**: Business payment arrangements
- **Credit Limit**: Financial limit in £ (GBP)
- **Estimate Count**: Number of estimates per customer
- **Advanced Filtering**: By status, payment terms, etc.

#### **Detailed Information Display**
- **Complete Overview**: All customer data in organised sections
- **Relationship Data**: Contacts and addresses with full details
- **Financial Summary**: Credit limits and payment terms
- **Navigation Links**: Click-through to related records

## 🧱 Material Size Management System

### **Material Size Overview**
Advanced size management allowing each material to have multiple available sizes with flexible pricing.

#### **Size Specifications**
- **Dimensions**: Width and height in millimetres
- **Size Names**: Custom names (A4, SRA3, Custom 700x1000, etc.)
- **Standard Size Recognition**: Automatic flagging of standard paper sizes
- **Area Calculation**: Automatic m² calculation from dimensions

#### **Flexible Pricing Models**
- **Cost per Sheet**: Fixed price per individual sheet
- **Cost per m²**: Price based on square metre area
- **Cost per Linear Metre**: For roll materials (vinyl, canvas, etc.)
- **Multiple Pricing**: Can use different models simultaneously

#### **Availability Management**
- **Availability Toggle**: Enable/disable sizes without deletion
- **Minimum Quantities**: Per-size minimum order requirements
- **Size-Specific Notes**: Additional information and restrictions

### **Standard Paper Sizes Included**
Pre-configured with industry-standard dimensions:

#### **A Series (ISO 216)**
- A0: 841mm × 1189mm
- A1: 594mm × 841mm  
- A2: 420mm × 594mm
- A3: 297mm × 420mm
- A4: 210mm × 297mm
- A5: 148mm × 210mm

#### **SRA Series (Supplementary Raw Format)**
- SRA0: 900mm × 1280mm
- SRA1: 640mm × 900mm
- SRA2: 450mm × 640mm  
- SRA3: 320mm × 450mm
- SRA4: 225mm × 320mm

#### **Custom Sizes**
- Business Card: 85mm × 55mm
- DL Flyer: 99mm × 210mm
- A6 Postcard: 105mm × 148mm
- Large Format options
- Banner sizes

### **Material Size Resource Features**

#### **Organised Form Sections**
- **Material & Size Information**: Material selection and size naming
- **Dimensions**: Width/height with real-time area calculation
- **Pricing**: All pricing models with £ currency
- **Availability & Settings**: Availability, minimum quantities, notes

#### **Advanced Table Display**
- **Material Name**: Shows parent material
- **Size Name**: Custom size identifier
- **Dimensions**: Formatted width × height display
- **Area**: Calculated square metres
- **Pricing**: Cost per sheet and per m² in £
- **Status Indicators**: Standard size and availability flags

#### **Integration Features**
- **Material Relation Manager**: Embedded in material view
- **Quick Size Creation**: Add sizes directly from material
- **Bulk Operations**: Manage multiple sizes efficiently
- **Advanced Filtering**: By material, standard sizes, availability

## 🔗 System Integration

### **Estimate-Customer Integration**
- **Optional Customer Linking**: Select customer when creating estimates
- **Customer Display**: Shows customer name in estimate tables
- **Navigation Links**: Click customer name to view customer details
- **Backward Compatibility**: Existing estimates work without customers

### **Enhanced Navigation Structure**
- **Customer Management**: New navigation group
  - Customers (with contacts and addresses)
- **Materials**: Reorganised navigation group  
  - Materials (with sizes relation manager)
  - Material Sizes (standalone management)
- **Print Setup**: Existing production setup
  - Production Methods
  - Machines

## 💷 British Localisation

### **Currency & Formatting**
- **£ (GBP)**: All monetary values in British pounds
- **British Spelling**: "Visualisation", "colour", "organisation"
- **UK Address Format**: County, postcode, "United Kingdom" default
- **Business Standards**: UK-standard payment terms and practices

### **Sample Data**
- **UK Companies**: Realistic British business examples
- **UK Addresses**: Proper UK address formatting
- **British Contact Details**: UK phone numbers and postcodes
- **Industry-Relevant**: Print and marketing industry examples

## 📊 Business Benefits

### **Customer Management**
- **Professional CRM**: Complete customer relationship tracking
- **Financial Control**: Credit limits and payment term management
- **Communication Management**: Multiple contacts with role-based access
- **Delivery Management**: Multiple addresses with special instructions
- **Business Intelligence**: Customer history and estimate tracking

### **Material Management**
- **Inventory Flexibility**: Multiple sizes per material type
- **Pricing Accuracy**: Multiple pricing models for different scenarios
- **Standard Compliance**: Industry-standard paper sizes included
- **Cost Control**: Accurate pricing for different size configurations
- **Availability Management**: Control which sizes are offered

### **Operational Efficiency**
- **Streamlined Workflows**: Integrated customer and material selection
- **Data Consistency**: Normalised database design prevents duplication
- **Professional Presentation**: Comprehensive business information display
- **Scalable Architecture**: Supports business growth and complexity

This comprehensive CRUD system transforms the print estimation platform into a full business management solution, providing the foundation for professional print shop operations with proper customer relationship management and advanced material inventory control.
