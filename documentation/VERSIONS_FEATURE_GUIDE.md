# 📦 Versions Feature Guide

## 🎯 Overview
The versions feature allows you to create estimates for jobs that have multiple variations (versions) of the same item, each with different quantities. This is perfect for scenarios like:
- Different color variations of business cards
- Multiple sizes of the same design
- Various language versions of a brochure
- Different product variants

## ✨ Key Features

### 🔄 **Multi-Version Support**
- Add unlimited versions to a single estimate
- Each version has its own name and quantity
- Automatic calculation of total quantities
- Colour-coded visualisation for easy identification

### 📊 **Enhanced Calculations**
- **Total Quantity**: Automatically sums all version quantities
- **Version Distribution**: Shows how versions are distributed across sheets
- **Optimized Layout**: Calculates the most efficient arrangement
- **Cost Allocation**: Proportional cost distribution across versions

### 🎨 **Visual Imposition**
- **Colour-Coded Items**: Each version gets a unique colour
- **Version Labels**: Items show version identifier and item number
- **Distribution Charts**: Visual breakdown of version percentages
- **Sheet Planning**: Shows which versions go on which sheets

## 🚀 How to Use Versions

### **Step 1: Enable Versions in Estimate Calculator**
1. Go to **Estimate Calculator**
2. Toggle **"Multiple Versions"** switch
3. The versions section will appear below

### **Step 2: Add Version Details**
1. Click **"Add Version"** to create new versions
2. For each version, enter:
   - **Version Name**: e.g., "Red Cards", "Large Size", "English"
   - **Quantity**: Number of items for this version
3. Add as many versions as needed

### **Step 3: Calculate and Review**
1. Fill in other job details (dimensions, materials, etc.)
2. Click **"Calculate Estimate"**
3. Review the enhanced results showing:
   - Version breakdown with percentages
   - Colour-coded imposition layout
   - Distribution across sheets

## 📋 Example Scenarios

### **Business Cards - Multiple Colors**
```
Job: Business Cards for ABC Company
Item Size: 85mm × 55mm
Versions:
- Red Cards: 300 pieces
- Blue Cards: 200 pieces  
- Green Cards: 500 pieces
Total: 1,000 pieces
```

### **Brochures - Different Languages**
```
Job: Product Brochures
Item Size: 210mm × 297mm
Versions:
- English: 750 pieces
- Spanish: 150 pieces
- French: 100 pieces
Total: 1,000 pieces
```

### **Labels - Various Sizes**
```
Job: Product Labels
Versions:
- Small (50×30mm): 2,000 pieces
- Medium (75×45mm): 1,500 pieces
- Large (100×60mm): 500 pieces
Total: 4,000 pieces
```

## 🎨 Visual Features

### **Colour Coding System**
- **Version 1**: Blue (rgb(59 130 246))
- **Version 2**: Green (rgb(34 197 94))
- **Version 3**: Purple (rgb(168 85 247))
- **Version 4**: Orange (rgb(251 146 60))
- **Version 5**: Red (rgb(239 68 68))
- **Additional versions**: Cycles through colours

### **Item Labeling**
- Format: `[First Letter][Item Number]`
- Examples: `R1`, `R2`, `R3` for "Red Cards" version
- Hover tooltips show full version name and item number

### **Legend Display**
- Shows each version with its colour and quantity
- Format: `Version Name (Quantity)`
- Example: `Red Cards (300)`

## 📊 Enhanced Information Display

### **Version Breakdown Panel**
- **Percentage Distribution**: Shows what % each version represents
- **Sheet Requirements**: How many sheets each version needs
- **Items on Last Sheet**: Partial sheet utilization

### **Calculation Results**
- **Total Quantity**: Sum of all versions
- **Items per Sheet**: Based on item dimensions and sheet size
- **Sheets Required**: Total sheets needed for all versions
- **Layout Efficiency**: Overall efficiency percentage

### **Cost Analysis**
- **Cost per Item**: Total price ÷ total quantity
- **Proportional Costing**: Costs distributed by version quantity
- **Material Optimization**: Efficient use of sheet space

## 🔍 Viewing Saved Estimates

### **In Estimate List**
- Shows total quantity for versioned estimates
- Status and pricing information
- Quick access to detailed view

### **In Estimate Detail View**
- **Versions Section**: Lists all versions with quantities and percentages
- **Enhanced Visualisation**: Colour-coded imposition with version distribution
- **Version Distribution Cards**: Individual breakdown for each version
- **Production Planning**: Sheet requirements per version

## 💡 Best Practices

### **Version Naming**
- Use clear, descriptive names: "Red Cards" vs "Version A"
- Include key differentiators: "Large English", "Small Spanish"
- Keep names concise for better visualization

### **Quantity Planning**
- Consider minimum quantities for each version
- Plan for waste and overruns per version
- Balance versions for efficient sheet utilization

### **Layout Optimization**
- Test different sheet sizes for better efficiency
- Consider grouping similar quantities
- Review imposition for optimal cutting

## 🛠️ Technical Details

### **Database Structure**
- `has_versions`: Boolean flag indicating multi-version estimate
- `versions`: JSON array storing version details
- `total_quantity`: Calculated sum of all version quantities
- `quantity`: Nullable for versioned estimates

### **Calculation Logic**
- **Imposition**: Calculates optimal layout for total quantity
- **Distribution**: Assigns versions to specific positions
- **Efficiency**: Maintains same efficiency calculations
- **Costing**: Uses total quantity for all cost calculations

### **API Integration**
- All existing APIs work with versioned estimates
- Additional version data included in responses
- Backward compatibility maintained

## 🎯 Benefits

### **For Print Shops**
- **Accurate Quoting**: Precise calculations for complex jobs
- **Visual Planning**: Clear understanding of production requirements
- **Efficient Production**: Optimized sheet utilization
- **Professional Presentation**: Detailed breakdowns for clients

### **For Clients**
- **Transparent Pricing**: Clear cost breakdown by version
- **Visual Confirmation**: See exactly how items will be arranged
- **Flexible Quantities**: Easy to adjust version quantities
- **Professional Documentation**: Detailed estimates for approval

This versions feature transforms the estimation process from simple quantity-based calculations to sophisticated multi-variant job planning, providing unprecedented visibility into complex print production requirements.
