# 📊 Estimate Visualisation Guide

## 🎯 Overview
The estimate view page now includes comprehensive imposition visualisation and detailed cost breakdowns. This allows you to see exactly how items will be arranged on sheets and understand all cost components.

## 🚀 How to Access

### Method 1: From Estimates List
1. Go to **Admin Panel** → **Estimates** → **Estimates**
2. Find your estimate in the table
3. Click **"View Details"** or **"View Layout"** button
4. You'll be taken to `/admin/estimates/{id}`

### Method 2: After Creating an Estimate
1. Use the **Estimate Calculator** to create a new estimate
2. Click **"Save Estimate"** after calculating
3. You'll be automatically redirected to the estimate view page

### Method 3: Direct URL
- Navigate directly to: `http://filament-mis.test/admin/estimates/{estimate_id}`

## 📋 What You'll See

### 1. Job Information Section
- **Estimate Number**: Auto-generated unique identifier
- **Status**: Draft, Sent, Approved, or Rejected (with color badges)
- **Client Name** and **Job Title**
- **Description** (if provided)

### 2. Specifications Section
- **Quantity**: Number of items needed
- **Item Dimensions**: Width × Height in mm
- **Sheet Dimensions**: Sheet/roll width × height in mm

### 3. Production Setup Section
- **Production Method**: Digital Print, Litho Print, or Wide Format
- **Machine**: Selected machine with its capabilities
- **Material**: Chosen material with specifications

### 4. Imposition Results Section
- **Items per Sheet**: How many items fit on one sheet
- **Sheets Required**: Total sheets needed for the job
- **Layout Efficiency**: Percentage of sheet area used
- **Waste**: Percentage of sheet area wasted

### 5. Cost Breakdown Section
- **Material Cost**: Cost of paper/vinyl/fabric
- **Production Cost**: Machine time costs (including waste)
- **Setup Cost**: One-time setup fee
- **Total Cost**: Sum of all costs
- **Markup**: Applied markup percentage
- **Final Price**: Total price to quote (highlighted in green)

## 🎨 Imposition Visualization

### Visual Layout Display
- **Sheet Representation**: Gray-bordered rectangle showing the sheet
- **Item Positioning**: Blue rectangles showing where each item is placed
- **Item Numbers**: Each item is numbered (1, 2, 3, etc.)
- **Proportional Scaling**: Layout maintains correct proportions
- **Rotation Indicator**: Orange icon if items are rotated for better fit

### Layout Statistics Panel
- **Grid Layout**: Shows arrangement (e.g., "2 × 5" = 2 horizontal, 5 vertical)
- **Items per Sheet**: Total items that fit
- **Efficiency**: Percentage of sheet area utilized
- **Waste**: Percentage of sheet area unused
- **Bleed**: Bleed amount around each item
- **Gutter**: Space between items
- **Item Dimensions**: With and without bleed

### Production Information Cards
1. **Layout Efficiency Card** (Blue)
   - Shows efficiency percentage
   - Higher is better (less waste)

2. **Cost per Item Card** (Green)
   - Final price divided by quantity
   - Useful for pricing individual items

3. **Production Time Card** (Purple)
   - Estimated machine time in hours
   - Based on machine speed and sheet count

## 💡 Understanding the Visualization

### Reading the Layout
- **Sheet**: The outer gray rectangle represents your sheet/roll
- **Items**: Blue rectangles show where each item will be printed
- **Numbers**: Help identify item positions for cutting/finishing
- **Spacing**: White space between items shows gutter and bleed areas

### Efficiency Indicators
- **High Efficiency (>80%)**: Green indicators, minimal waste
- **Medium Efficiency (60-80%)**: Yellow indicators, acceptable waste
- **Low Efficiency (<60%)**: Red indicators, consider different sheet size

### Rotation Benefits
- Items may be automatically rotated 90° for better fit
- Orange rotation icon indicates when this optimization is applied
- This can significantly improve efficiency and reduce costs

## 🔧 Actions Available

### From the Estimate View Page
1. **Edit**: Modify estimate details
2. **Duplicate**: Create a copy with same specifications
3. **Back to List**: Return to estimates overview

### Duplicate Functionality
- Copies all job specifications
- Adds "(Copy)" to job title
- Allows quick creation of similar estimates
- Useful for variations or revisions

## 📊 Example Scenarios

### Business Cards (85×55mm on A4)
- **Expected Layout**: 2×5 grid (10 cards per sheet)
- **Efficiency**: ~85-90%
- **Good for**: Small quantities, quick turnaround

### A4 Flyers (210×297mm on SRA3)
- **Expected Layout**: 1×1 (1 flyer per sheet)
- **Efficiency**: ~75-80% (due to bleed requirements)
- **Good for**: High-quality printing with bleed

### Labels (50×30mm on A4)
- **Expected Layout**: 4×9 grid (36 labels per sheet)
- **Efficiency**: ~90-95%
- **Good for**: High-volume label production

## 🎯 Tips for Best Results

1. **Choose Appropriate Sheet Sizes**: Use the calculator to test different sheet sizes
2. **Consider Rotation**: Allow items to rotate for better efficiency
3. **Optimize Bleed/Gutter**: Adjust spacing for your finishing requirements
4. **Review Efficiency**: Aim for >75% efficiency when possible
5. **Check Production Time**: Balance efficiency with production speed

## 🔍 Troubleshooting

### No Visualization Showing
- Check that the estimate has imposition_layout data
- Ensure the estimate was created through the calculator
- Verify items fit on the selected sheet size

### Low Efficiency
- Try different sheet sizes
- Reduce bleed/gutter if possible
- Consider rotating items
- Check if items are too large for the sheet

### Unexpected Layout
- Verify item dimensions are correct
- Check sheet dimensions match your material
- Ensure bleed and gutter settings are appropriate

This visualization system provides complete transparency into how your print jobs will be produced, helping you optimize costs and communicate clearly with clients about pricing and production requirements.
