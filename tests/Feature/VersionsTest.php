<?php

use App\Services\EstimateService;
use App\Services\ImpositionService;
use Illuminate\Foundation\Testing\RefreshDatabase;

uses(RefreshDatabase::class);

test('imposition service calculates versions correctly', function () {
    $impositionService = new ImpositionService();

    $versions = [
        ['name' => 'Version A', 'quantity' => 600],
        ['name' => 'Version B', 'quantity' => 400],
    ];

    $result = $impositionService->calculateImpositionWithVersions(
        itemWidth: 100,
        itemHeight: 150,
        sheetWidth: 700,
        sheetHeight: 1000,
        versions: $versions
    );

    expect($result['total_quantity'])->toBe(1000);
    expect($result['versions'])->toHaveCount(2);
    expect($result['version_distribution'])->toHaveCount(2);
    expect($result['positions_with_versions'])->toBeArray();

    // Check version distribution
    $versionA = collect($result['version_distribution'])->firstWhere('name', 'Version A');
    $versionB = collect($result['version_distribution'])->firstWhere('name', 'Version B');

    expect($versionA['quantity'])->toBe(600);
    expect($versionA['percentage'])->toBe(60.0);
    expect($versionB['quantity'])->toBe(400);
    expect($versionB['percentage'])->toBe(40.0);
});

test('estimate service creates estimate with versions', function () {
    $this->seed();

    $estimateService = new EstimateService(new ImpositionService());

    $data = [
        'client_name' => 'Test Client',
        'job_title' => 'Multi-Version Job',
        'production_method_id' => 1,
        'machine_id' => 1,
        'material_id' => 1,
        'has_versions' => true,
        'versions' => [
            ['name' => 'Red Version', 'quantity' => 300],
            ['name' => 'Blue Version', 'quantity' => 200],
            ['name' => 'Green Version', 'quantity' => 500],
        ],
        'item_width' => 100,
        'item_height' => 150,
        'sheet_width' => 700,
        'sheet_height' => 1000,
        'markup_percentage' => 25,
    ];

    $estimate = $estimateService->createEstimate($data);

    expect($estimate->has_versions)->toBeTrue();
    expect($estimate->versions)->toHaveCount(3);
    expect($estimate->total_quantity)->toBe(1000);
    expect($estimate->quantity)->toBeNull();

    // Check versions data
    expect($estimate->versions[0]['name'])->toBe('Red Version');
    expect($estimate->versions[0]['quantity'])->toBe(300);
    expect($estimate->versions[1]['name'])->toBe('Blue Version');
    expect($estimate->versions[1]['quantity'])->toBe(200);
    expect($estimate->versions[2]['name'])->toBe('Green Version');
    expect($estimate->versions[2]['quantity'])->toBe(500);

    // Check imposition layout includes version data
    expect($estimate->imposition_layout['versions'])->toHaveCount(3);
    expect($estimate->imposition_layout['total_quantity'])->toBe(1000);
    expect($estimate->imposition_layout['version_distribution'])->toHaveCount(3);
});

test('estimate model calculates total quantity correctly', function () {
    $this->seed();

    $estimateService = new EstimateService(new ImpositionService());

    // Test with versions
    $estimateWithVersions = $estimateService->createEstimate([
        'client_name' => 'Test Client',
        'job_title' => 'Versioned Job',
        'production_method_id' => 1,
        'machine_id' => 1,
        'material_id' => 1,
        'has_versions' => true,
        'versions' => [
            ['name' => 'A', 'quantity' => 250],
            ['name' => 'B', 'quantity' => 750],
        ],
        'item_width' => 100,
        'item_height' => 150,
        'sheet_width' => 700,
        'sheet_height' => 1000,
        'markup_percentage' => 25,
    ]);

    expect($estimateWithVersions->total_quantity)->toBe(1000);
    expect($estimateWithVersions->hasMultipleVersions())->toBeTrue();

    // Test without versions
    $estimateWithoutVersions = $estimateService->createEstimate([
        'client_name' => 'Test Client',
        'job_title' => 'Single Version Job',
        'production_method_id' => 1,
        'machine_id' => 1,
        'material_id' => 1,
        'quantity' => 500,
        'item_width' => 100,
        'item_height' => 150,
        'sheet_width' => 700,
        'sheet_height' => 1000,
        'markup_percentage' => 25,
    ]);

    expect($estimateWithoutVersions->total_quantity)->toBe(500);
    expect($estimateWithoutVersions->hasMultipleVersions())->toBeFalse();
});

test('handles division by zero safely', function () {
    $this->seed();

    $estimateService = new EstimateService(new ImpositionService());

    // Create estimate with versions
    $estimate = $estimateService->createEstimate([
        'client_name' => 'Test Client',
        'job_title' => 'Division Test',
        'production_method_id' => 1,
        'machine_id' => 1,
        'material_id' => 1,
        'has_versions' => true,
        'versions' => [
            ['name' => 'Test Version', 'quantity' => 100],
        ],
        'item_width' => 100,
        'item_height' => 150,
        'sheet_width' => 700,
        'sheet_height' => 1000,
        'markup_percentage' => 25,
    ]);

    // Test that total_quantity is used instead of quantity
    expect($estimate->quantity)->toBeNull();
    expect($estimate->total_quantity)->toBe(100);
    expect($estimate->final_price)->toBeGreaterThan(0);

    // Test cost per item calculation doesn't cause division by zero
    $costPerItem = $estimate->final_price / $estimate->total_quantity;
    expect($costPerItem)->toBeGreaterThan(0);
    expect($costPerItem)->toBeFloat();
});

test('ganged layout shows multiple versions in visualization', function () {
    $impositionService = new ImpositionService();

    $versions = [
        ['name' => 'Version A', 'quantity' => 700],
        ['name' => 'Version B', 'quantity' => 300],
    ];

    $result = $impositionService->calculateImpositionWithVersions(
        itemWidth: 100,
        itemHeight: 150,
        sheetWidth: 700,
        sheetHeight: 1000,
        versions: $versions,
        layoutMode: 'ganged'
    );

    expect($result['layout_mode'])->toBe('ganged');
    expect($result['positions_with_versions'])->toBeArray();

    // Check that both versions are represented in positions
    $versionNames = array_unique(array_column($result['positions_with_versions'], 'version_name'));
    expect($versionNames)->toContain('Version A');
    expect($versionNames)->toContain('Version B');

    // Check proportional representation (approximately 70/30)
    $versionACounts = count(array_filter($result['positions_with_versions'], fn($pos) => $pos['version_name'] === 'Version A'));
    $versionBCounts = count(array_filter($result['positions_with_versions'], fn($pos) => $pos['version_name'] === 'Version B'));

    expect($versionACounts)->toBeGreaterThan($versionBCounts); // Version A should have more positions
    expect($versionACounts + $versionBCounts)->toBe(count($result['positions_with_versions']));
});

test('separate layout calculates individual sheet requirements', function () {
    $impositionService = new ImpositionService();

    $versions = [
        ['name' => 'Red Cards', 'quantity' => 500],
        ['name' => 'Blue Cards', 'quantity' => 300],
    ];

    $result = $impositionService->calculateImpositionWithVersions(
        itemWidth: 85,
        itemHeight: 55,
        sheetWidth: 700,
        sheetHeight: 1000,
        versions: $versions,
        layoutMode: 'separate'
    );

    expect($result['layout_mode'])->toBe('separate');
    expect($result['version_distribution'])->toHaveCount(2);

    // Check that each version has sheet range information
    foreach ($result['version_distribution'] as $version) {
        expect($version)->toHaveKeys(['sheet_start', 'sheet_end', 'sheets_needed']);
        expect($version['sheet_start'])->toBeGreaterThan(0);
        expect($version['sheet_end'])->toBeGreaterThanOrEqual($version['sheet_start']);
    }

    // Verify sheet ranges don't overlap
    $redCards = collect($result['version_distribution'])->firstWhere('name', 'Red Cards');
    $blueCards = collect($result['version_distribution'])->firstWhere('name', 'Blue Cards');

    expect($redCards['sheet_end'])->toBeLessThan($blueCards['sheet_start']);
});
