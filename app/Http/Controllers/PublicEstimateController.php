<?php

namespace App\Http\Controllers;

use App\Models\Estimate;
use App\Models\Job;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Str;

class PublicEstimateController extends Controller
{
    public function show(Request $request, $hash)
    {
        $estimate = Estimate::where('public_hash', $hash)->firstOrFail();

        return view('estimates.public-view', compact('estimate'));
    }

    public function accept(Request $request, $hash)
    {
        $estimate = Estimate::where('public_hash', $hash)->firstOrFail();

        if ($estimate->status === 'approved') {
            return redirect()->route('estimates.public.show', $hash)
                ->with('error', 'This estimate has already been approved.');
        }

        // Create job from estimate
        $job = Job::create([
            'job_number' => Job::generateJobNumber(),
            'estimate_id' => $estimate->id,
            'customer_id' => $estimate->customer_id,
            'customer_contact_id' => $estimate->customer_contact_id,
            'client_name' => $estimate->client_name,
            'job_title' => $estimate->job_title,
            'description' => $estimate->description,
            'status' => 'pending',
            'production_method_id' => $estimate->production_method_id,
            'machine_id' => $estimate->machine_id,
            'material_id' => $estimate->material_id,
            'material_size_id' => $estimate->material_size_id,
            'quantity' => $estimate->quantity,
            'has_versions' => $estimate->has_versions,
            'versions' => $estimate->versions,
            'total_quantity' => $estimate->total_quantity,
            'item_width' => $estimate->item_width,
            'item_height' => $estimate->item_height,
            'sheet_width' => $estimate->sheet_width,
            'sheet_height' => $estimate->sheet_height,
            'items_per_sheet' => $estimate->items_per_sheet,
            'sheets_required' => $estimate->sheets_required,
            'imposition_layout' => $estimate->imposition_layout,
            'material_cost' => $estimate->material_cost,
            'production_cost' => $estimate->production_cost,
            'setup_cost' => $estimate->setup_cost,
            'total_cost' => $estimate->total_cost,
            'markup_percentage' => $estimate->markup_percentage,
            'final_price' => $estimate->final_price,
        ]);

        // Update estimate status
        $estimate->update(['status' => 'approved']);

        return redirect()->route('estimates.public.show', $hash)
            ->with('success', 'Estimate approved! Job ' . $job->job_number . ' has been created.');
    }

    public function decline(Request $request, $hash)
    {
        $estimate = Estimate::where('public_hash', $hash)->firstOrFail();

        $request->validate([
            'reason' => 'nullable|string|max:500'
        ]);

        $estimate->update([
            'status' => 'rejected',
            'rejection_reason' => $request->reason
        ]);

        return redirect()->route('estimates.public.show', $hash)
            ->with('success', 'Estimate declined. We appreciate your feedback.');
    }

    public function email(Estimate $estimate)
    {
        // Generate public hash if not exists
        if (!$estimate->public_hash) {
            $estimate->update([
                'public_hash' => Str::random(32)
            ]);
        }

        // Send email to the selected contact or fallback to primary contact
        $emailAddress = null;
        if ($estimate->customerContact) {
            $emailAddress = $estimate->customerContact->email;
        } elseif ($estimate->customer) {
            $primaryContact = $estimate->customer->contacts()->where('receives_quotes', true)->first();
            $emailAddress = $primaryContact ? $primaryContact->email : null;
        }

        if ($emailAddress) {
            Mail::to($emailAddress)->send(new \App\Mail\EstimateEmail($estimate));
        } else {
            return redirect()->back()->with('error', 'No email address found for this estimate.');
        }

        return redirect()->back()->with('success', 'Estimate email sent successfully!');
    }
}
