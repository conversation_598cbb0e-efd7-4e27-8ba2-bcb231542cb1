<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Casts\Attribute;

class Job extends Model
{
    use HasFactory;

    protected $table = 'print_jobs';

    protected $fillable = [
        'job_number',
        'estimate_id',
        'customer_id',
        'customer_contact_id',
        'client_name',
        'job_title',
        'description',
        'notes',
        'status',
        'due_date',
        'started_at',
        'completed_at',
        'production_method_id',
        'machine_id',
        'material_id',
        'material_size_id',
        'quantity',
        'has_versions',
        'versions',
        'total_quantity',
        'item_width',
        'item_height',
        'sheet_width',
        'sheet_height',
        'items_per_sheet',
        'sheets_required',
        'imposition_layout',
        'material_cost',
        'production_cost',
        'setup_cost',
        'total_cost',
        'markup_percentage',
        'final_price',
        'actual_material_cost',
        'actual_production_cost',
        'actual_total_cost',
    ];

    protected $casts = [
        'has_versions' => 'boolean',
        'versions' => 'array',
        'imposition_layout' => 'array',
        'due_date' => 'datetime',
        'started_at' => 'datetime',
        'completed_at' => 'datetime',
        'material_cost' => 'decimal:2',
        'production_cost' => 'decimal:2',
        'setup_cost' => 'decimal:2',
        'total_cost' => 'decimal:2',
        'markup_percentage' => 'decimal:2',
        'final_price' => 'decimal:2',
        'actual_material_cost' => 'decimal:2',
        'actual_production_cost' => 'decimal:2',
        'actual_total_cost' => 'decimal:2',
    ];

    // Relationships
    public function estimate(): BelongsTo
    {
        return $this->belongsTo(Estimate::class);
    }

    public function customer(): BelongsTo
    {
        return $this->belongsTo(Customer::class);
    }

    public function customerContact(): BelongsTo
    {
        return $this->belongsTo(CustomerContact::class);
    }

    public function productionMethod(): BelongsTo
    {
        return $this->belongsTo(ProductionMethod::class);
    }

    public function machine(): BelongsTo
    {
        return $this->belongsTo(Machine::class);
    }

    public function material(): BelongsTo
    {
        return $this->belongsTo(Material::class);
    }

    public function materialSize(): BelongsTo
    {
        return $this->belongsTo(MaterialSize::class);
    }

    // Accessors
    public function getStatusBadgeColorAttribute(): string
    {
        return match($this->status) {
            'pending' => 'warning',
            'in_progress' => 'info',
            'completed' => 'success',
            'cancelled' => 'danger',
            'on_hold' => 'gray',
            default => 'gray',
        };
    }

    public function getStatusLabelAttribute(): string
    {
        return match($this->status) {
            'pending' => 'Pending',
            'in_progress' => 'In Progress',
            'completed' => 'Completed',
            'cancelled' => 'Cancelled',
            'on_hold' => 'On Hold',
            default => 'Unknown',
        };
    }

    public function getIsOverdueAttribute(): bool
    {
        return $this->due_date &&
               $this->due_date < now()->toDate() &&
               !in_array($this->status, ['completed', 'cancelled']);
    }

    public function getDaysUntilDueAttribute(): ?int
    {
        if (!$this->due_date) {
            return null;
        }

        return now()->diffInDays($this->due_date, false);
    }

    // Scopes
    public function scopeActive($query)
    {
        return $query->whereNotIn('status', ['completed', 'cancelled']);
    }

    public function scopeOverdue($query)
    {
        return $query->where('due_date', '<', now())
                    ->whereNotIn('status', ['completed', 'cancelled']);
    }

    public function scopeByStatus($query, string $status)
    {
        return $query->where('status', $status);
    }

    // Methods
    public function markAsStarted(): void
    {
        $this->update([
            'status' => 'in_progress',
            'started_at' => now(),
        ]);
    }

    public function markAsCompleted(): void
    {
        $this->update([
            'status' => 'completed',
            'completed_at' => now(),
        ]);
    }

    public function calculateProfitMargin(): float
    {
        if (!$this->actual_total_cost || $this->final_price <= 0) {
            return 0;
        }

        return (($this->final_price - $this->actual_total_cost) / $this->final_price) * 100;
    }

    public static function generateJobNumber(): string
    {
        $year = now()->year;
        $lastJob = static::whereYear('created_at', $year)
            ->orderBy('id', 'desc')
            ->first();

        $nextNumber = $lastJob ?
            (int) substr($lastJob->job_number, -4) + 1 :
            1;

        return 'JOB' . $year . str_pad($nextNumber, 4, '0', STR_PAD_LEFT);
    }
}
