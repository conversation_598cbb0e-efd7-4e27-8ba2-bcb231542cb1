<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class CustomerContact extends Model
{
    use HasFactory;

    protected $fillable = [
        'customer_id',
        'first_name',
        'last_name',
        'job_title',
        'email',
        'phone',
        'mobile',
        'is_primary',
        'receives_quotes',
        'receives_invoices',
        'notes',
    ];

    protected $casts = [
        'is_primary' => 'boolean',
        'receives_quotes' => 'boolean',
        'receives_invoices' => 'boolean',
    ];

    // Relationships
    public function customer(): BelongsTo
    {
        return $this->belongsTo(Customer::class);
    }

    // Accessors
    public function getFullNameAttribute(): string
    {
        return trim($this->first_name . ' ' . $this->last_name);
    }

    public function getDisplayNameAttribute(): string
    {
        $name = $this->full_name;
        if ($this->job_title) {
            $name .= ' (' . $this->job_title . ')';
        }
        return $name;
    }

    // Helper methods
    public function makesPrimary(): void
    {
        // Remove primary status from other contacts
        $this->customer->contacts()->where('id', '!=', $this->id)->update(['is_primary' => false]);

        // Set this contact as primary
        $this->update(['is_primary' => true]);
    }
}
