<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Support\Str;

class Estimate extends Model
{
    protected $fillable = [
        'estimate_number',
        'customer_id',
        'customer_contact_id',
        'client_name',
        'job_title',
        'description',
        'production_method_id',
        'machine_id',
        'material_id',
        'size_id',
        'quantity',
        'has_versions',
        'versions',
        'total_quantity',
        'item_width',
        'item_height',
        'sheet_width',
        'sheet_height',
        'items_per_sheet',
        'sheets_required',
        'material_cost',
        'production_cost',
        'setup_cost',
        'total_cost',
        'markup_percentage',
        'final_price',
        'imposition_layout',
        'status',
        'public_hash',
        'rejection_reason',
        'sent_at',
        'approved_at',
    ];

    protected $casts = [
        'has_versions' => 'boolean',
        'versions' => 'array',
        'material_cost' => 'decimal:2',
        'production_cost' => 'decimal:2',
        'setup_cost' => 'decimal:2',
        'total_cost' => 'decimal:2',
        'markup_percentage' => 'decimal:2',
        'final_price' => 'decimal:2',
        'imposition_layout' => 'array',
        'sent_at' => 'datetime',
        'approved_at' => 'datetime',
    ];

    protected static function boot()
    {
        parent::boot();

        static::creating(function ($estimate) {
            if (empty($estimate->estimate_number)) {
                $estimate->estimate_number = 'EST-' . date('Y') . '-' . str_pad(
                    static::whereYear('created_at', date('Y'))->count() + 1,
                    4,
                    '0',
                    STR_PAD_LEFT
                );
            }
        });
    }

    public function customer(): BelongsTo
    {
        return $this->belongsTo(Customer::class);
    }

    public function customerContact(): BelongsTo
    {
        return $this->belongsTo(CustomerContact::class);
    }

    public function parts(): HasMany
    {
        return $this->hasMany(EstimatePart::class);
    }

    public function productionMethod(): BelongsTo
    {
        return $this->belongsTo(ProductionMethod::class);
    }

    public function machine(): BelongsTo
    {
        return $this->belongsTo(Machine::class);
    }

    public function material(): BelongsTo
    {
        return $this->belongsTo(Material::class);
    }

    public function size(): BelongsTo
    {
        return $this->belongsTo(Size::class);
    }

    public function items(): HasMany
    {
        return $this->hasMany(EstimateItem::class);
    }

    public function scopeByStatus($query, string $status)
    {
        return $query->where('status', $status);
    }

    public function calculateTotalWithMarkup(): float
    {
        return $this->total_cost * (1 + ($this->markup_percentage / 100));
    }

    public function getItemAreaAttribute(): float
    {
        return ($this->item_width * $this->item_height) / 1000000; // Convert mm² to m²
    }

    public function getSheetAreaAttribute(): float
    {
        return ($this->sheet_width * $this->sheet_height) / 1000000; // Convert mm² to m²
    }

    public function getEfficiencyPercentageAttribute(): float
    {
        // If we have imposition layout data, use that efficiency
        if ($this->imposition_layout && isset($this->imposition_layout['efficiency_percentage'])) {
            return $this->imposition_layout['efficiency_percentage'];
        }

        // Fallback calculation
        if ($this->sheet_area <= 0) {
            return 0;
        }

        return ($this->items_per_sheet * $this->item_area) / $this->sheet_area * 100;
    }

    public function getTotalQuantityAttribute(): int
    {
        if ($this->has_versions && $this->versions) {
            return collect($this->versions)->sum('quantity');
        }

        return $this->attributes['quantity'] ?? 0;
    }

    public function getVersionsListAttribute(): array
    {
        if (!$this->has_versions || !$this->versions) {
            return [];
        }

        return collect($this->versions)->map(function ($version) {
            return [
                'name' => $version['name'],
                'quantity' => $version['quantity'],
                'percentage' => $this->total_quantity > 0 ? ($version['quantity'] / $this->total_quantity) * 100 : 0,
            ];
        })->toArray();
    }

    public function hasMultipleVersions(): bool
    {
        return $this->has_versions && $this->versions && count($this->versions) > 1;
    }

    public function getVersionByName(string $name): ?array
    {
        if (!$this->has_versions || !$this->versions) {
            return null;
        }

        return collect($this->versions)->firstWhere('name', $name);
    }
}
