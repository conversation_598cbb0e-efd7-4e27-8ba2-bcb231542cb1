<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class EstimatePart extends Model
{
    use HasFactory;

    protected $fillable = [
        'estimate_id',
        'product_id',
        'part_name',
        'description',
        'sort_order',
        'production_method_id',
        'machine_id',
        'material_id',
        'size_id',
        'quantity',
        'has_versions',
        'versions',
        'total_quantity',
        'item_width',
        'item_height',
        'sheet_width',
        'sheet_height',
        'items_per_sheet',
        'sheets_required',
        'imposition_layout',
        'optimized',
        'material_cost',
        'production_cost',
        'setup_cost',
        'total_cost',
        'markup_percentage',
        'final_price',
        'pages',
        'binding_type',
        'fold_type',
        'finishing_options',
    ];

    protected $casts = [
        'has_versions' => 'boolean',
        'versions' => 'array',
        'imposition_layout' => 'array',
        'optimized' => 'boolean',
        'material_cost' => 'decimal:2',
        'production_cost' => 'decimal:2',
        'setup_cost' => 'decimal:2',
        'total_cost' => 'decimal:2',
        'markup_percentage' => 'decimal:2',
        'final_price' => 'decimal:2',
        'finishing_options' => 'array',
    ];

    // Relationships
    public function estimate(): BelongsTo
    {
        return $this->belongsTo(Estimate::class);
    }

    public function product(): BelongsTo
    {
        return $this->belongsTo(Product::class);
    }

    public function productionMethod(): BelongsTo
    {
        return $this->belongsTo(ProductionMethod::class);
    }

    public function machine(): BelongsTo
    {
        return $this->belongsTo(Machine::class);
    }

    public function material(): BelongsTo
    {
        return $this->belongsTo(Material::class);
    }

    public function size(): BelongsTo
    {
        return $this->belongsTo(Size::class);
    }

    // Scopes
    public function scopeOrdered($query)
    {
        return $query->orderBy('sort_order')->orderBy('part_name');
    }

    // Accessors
    public function getDisplayNameAttribute(): string
    {
        return $this->part_name;
    }

    public function getPricePerItemAttribute(): float
    {
        return $this->total_quantity > 0 ? $this->final_price / $this->total_quantity : 0;
    }

    // Methods
    public function calculateTotalQuantity(): int
    {
        if ($this->has_versions && $this->versions) {
            return collect($this->versions)->sum('quantity');
        }

        return $this->quantity ?? 0;
    }

    public function getVersionsBreakdown(): array
    {
        if (!$this->has_versions || !$this->versions) {
            return [];
        }

        return collect($this->versions)->map(function ($version) {
            return [
                'name' => $version['name'],
                'quantity' => $version['quantity'],
                'percentage' => $this->total_quantity > 0 ? ($version['quantity'] / $this->total_quantity) * 100 : 0,
            ];
        })->toArray();
    }

    public function isCompatibleWithProduct(): bool
    {
        return $this->product->isCompatibleWithProductionMethod($this->production_method_id);
    }
}
