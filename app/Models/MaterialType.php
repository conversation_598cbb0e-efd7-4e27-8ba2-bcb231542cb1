<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Str;

class MaterialType extends Model
{
    protected $fillable = [
        'name',
        'slug',
        'description',
        'active',
        'sort_order',
    ];

    protected $casts = [
        'active' => 'boolean',
    ];

    protected static function boot()
    {
        parent::boot();

        static::creating(function ($materialType) {
            if (empty($materialType->slug)) {
                $materialType->slug = Str::slug($materialType->name);
            }
        });

        static::updating(function ($materialType) {
            if ($materialType->isDirty('name') && empty($materialType->slug)) {
                $materialType->slug = Str::slug($materialType->name);
            }
        });
    }

    // Relationships
    public function materials(): HasMany
    {
        return $this->hasMany(Material::class);
    }

    // Scopes
    public function scopeActive(Builder $query): Builder
    {
        return $query->where('active', true);
    }

    public function scopeOrdered(Builder $query): Builder
    {
        return $query->orderBy('sort_order')->orderBy('name');
    }

    // Accessors
    public function getDisplayNameAttribute(): string
    {
        return $this->name;
    }
}
