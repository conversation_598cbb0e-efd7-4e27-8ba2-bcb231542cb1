<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class EstimateItem extends Model
{
    protected $fillable = [
        'estimate_id',
        'name',
        'version_name',
        'version_quantity',
        'width',
        'height',
        'quantity',
        'x_position',
        'y_position',
        'rotated',
    ];

    protected $casts = [
        'x_position' => 'decimal:2',
        'y_position' => 'decimal:2',
        'rotated' => 'boolean',
    ];

    public function estimate(): BelongsTo
    {
        return $this->belongsTo(Estimate::class);
    }

    public function getAreaAttribute(): float
    {
        return ($this->width * $this->height) / 1000000; // Convert mm² to m²
    }

    public function getDisplayDimensionsAttribute(): string
    {
        return "{$this->width}mm x {$this->height}mm";
    }

    public function getActualWidthAttribute(): int
    {
        return $this->rotated ? $this->height : $this->width;
    }

    public function getActualHeightAttribute(): int
    {
        return $this->rotated ? $this->width : $this->height;
    }
}
