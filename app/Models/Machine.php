<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Machine extends Model
{
    protected $fillable = [
        'production_method_id',
        'name',
        'type',
        'model',
        'hourly_rate',
        'setup_cost',
        'max_size_width',
        'max_size_height',
        'min_size_width',
        'min_size_height',
        'run_speed',
        'speed_unit',
        'waste_percentage',
        'is_active',
        'notes',
    ];

    protected $casts = [
        'hourly_rate' => 'decimal:2',
        'setup_cost' => 'decimal:2',
        'waste_percentage' => 'decimal:2',
        'is_active' => 'boolean',
    ];

    public function productionMethod(): BelongsTo
    {
        return $this->belongsTo(ProductionMethod::class);
    }

    public function estimates(): HasMany
    {
        return $this->hasMany(Estimate::class);
    }

    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    // Helper methods
    public function isSheetFed(): bool
    {
        return $this->type === 'sheet-fed';
    }

    public function isRollFed(): bool
    {
        return $this->type === 'roll-fed';
    }

    public function canHandleSheetSize(int $width, int $height): bool
    {
        return $width <= $this->max_size_width
            && $height <= $this->max_size_height
            && $width >= $this->min_size_width
            && $height >= $this->min_size_height;
    }

    public function calculateProductionTime(int $quantity): float
    {
        if ($this->run_speed <= 0) {
            return 0;
        }

        // For sheet-fed machines, quantity is sheets
        // For roll-fed machines, quantity could be linear metres
        return $quantity / $this->run_speed;
    }

    public function calculateRunCost(int $quantity): float
    {
        $hours = $this->calculateProductionTime($quantity);
        return $hours * $this->hourly_rate;
    }

    public function calculateTotalCost(int $quantity): float
    {
        return $this->setup_cost + $this->calculateRunCost($quantity);
    }

    public function getSpeedDisplayAttribute(): string
    {
        return number_format($this->run_speed) . ' ' . $this->speed_unit;
    }

    public function getSizeConstraintsAttribute(): string
    {
        return "Min: {$this->min_size_width}×{$this->min_size_height}mm, Max: {$this->max_size_width}×{$this->max_size_height}mm";
    }
}
