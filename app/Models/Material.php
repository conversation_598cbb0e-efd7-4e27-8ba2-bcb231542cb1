<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class Material extends Model
{
    use HasFactory;
    protected $fillable = [
        'material_type_id',
        'name',
        'finish',
        'weight',
        'caliper',
        // Sheet costing
        'cost_per_ton',
        // Roll costing
        'roll_cost',
        'roll_length_meters',
        'roll_width',
        'is_active',
        'notes',
    ];

    protected $casts = [
        'cost_per_ton' => 'decimal:2',
        'roll_cost' => 'decimal:2',
        'roll_length_meters' => 'decimal:2',
        'is_active' => 'boolean',
    ];

    public function materialType(): BelongsTo
    {
        return $this->belongsTo(MaterialType::class);
    }

    public function estimates(): HasMany
    {
        return $this->hasMany(Estimate::class);
    }

    public function materialSizes(): HasMany
    {
        return $this->hasMany(Size::class);
    }

    public function sizes(): BelongsToMany
    {
        return $this->belongsToMany(Size::class, 'material_sizes')
            ->withPivot(['ream_quantity', 'bulk_quantity', 'is_available'])
            ->withTimestamps();
    }

    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    public function scopeSheets($query)
    {
        return $query->whereHas('materialType', function ($q) {
            $q->where('slug', 'sheet');
        });
    }

    public function scopeRolls($query)
    {
        return $query->whereHas('materialType', function ($q) {
            $q->where('slug', 'roll');
        });
    }

    // Helper methods to determine material type
    public function isRoll(): bool
    {
        return $this->materialType?->slug === 'roll';
    }

    public function isSheet(): bool
    {
        return $this->materialType?->slug === 'sheet';
    }

    /**
     * Calculate cost per sheet from cost per ton (for sheets only)
     * Requires MaterialSize dimensions
     */
    public function calculateCostPerSheet(float $sheetWidth, float $sheetHeight): float
    {
        if ($this->isRoll() || !$this->cost_per_ton || !$this->weight) {
            return 0;
        }

        // Calculate sheet area in square meters
        $sheetAreaSqm = ($sheetWidth * $sheetHeight) / 1000000; // mm² to m²

        // Calculate sheet weight in kg
        $sheetWeightKg = ($sheetAreaSqm * $this->weight) / 1000; // gsm to kg

        // Calculate cost per sheet
        return ($this->cost_per_ton / 1000) * $sheetWeightKg;
    }

    /**
     * Calculate cost per 1000 sheets (more relevant for print industry)
     */
    public function calculateCostPer1000Sheets(float $sheetWidth, float $sheetHeight): float
    {
        return $this->calculateCostPerSheet($sheetWidth, $sheetHeight) * 1000;
    }

    /**
     * Calculate cost per sqm from cost per ton (for sheets only)
     */
    public function calculateCostPerSqm(): float
    {
        if ($this->isRoll() || !$this->cost_per_ton || !$this->weight) {
            return $this->cost_per_sqm ?? 0;
        }

        // Cost per sqm = (cost per ton / 1000) * weight in gsm / 1000
        return ($this->cost_per_ton / 1000) * ($this->weight / 1000);
    }

    /**
     * Calculate cost per linear meter from roll cost and length (for rolls only)
     */
    public function calculateCostPerLinearMeter(): float
    {
        if (!$this->isRoll() || !$this->roll_cost || !$this->roll_length_meters) {
            return $this->cost_per_linear_meter ?? 0;
        }

        return $this->roll_cost / $this->roll_length_meters;
    }

    /**
     * Calculate cost per sqm for rolls
     */
    public function calculateRollCostPerSqm(): float
    {
        if (!$this->isRoll() || !$this->roll_width) {
            return 0;
        }

        $costPerLinearMeter = $this->calculateCostPerLinearMeter();
        $widthInMeters = $this->roll_width / 1000; // mm to meters

        return $costPerLinearMeter / $widthInMeters;
    }

    public function calculateSheetCost(int $sheets, float $sheetWidth, float $sheetHeight): float
    {
        if ($this->isRoll()) {
            return 0;
        }

        return $sheets * $this->calculateCostPerSheet($sheetWidth, $sheetHeight);
    }

    public function calculateAreaCost(float $area): float
    {
        if ($this->isRoll()) {
            return $area * $this->calculateRollCostPerSqm();
        }

        return $area * $this->calculateCostPerSqm();
    }

    public function calculateLinearCost(float $length): float
    {
        if (!$this->isRoll()) {
            return 0;
        }

        return $length * $this->calculateCostPerLinearMeter();
    }

    public function getDisplayNameAttribute(): string
    {
        $parts = [$this->name];

        if ($this->weight) {
            $parts[] = $this->weight . 'gsm';
        }

        if ($this->finish) {
            $parts[] = ucfirst($this->finish);
        }

        return implode(' - ', $parts);
    }
}
