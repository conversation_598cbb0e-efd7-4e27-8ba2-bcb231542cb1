<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Builder;

class Size extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'width_mm',
        'height_mm',
        'description',
        'is_active',
        'sort_order',
    ];

    protected $casts = [
        'is_active' => 'boolean',
    ];

    // Relationships
    public function materialSizes(): HasMany
    {
        return $this->hasMany(MaterialSize::class);
    }

    // Scopes
    public function scopeActive(Builder $query): Builder
    {
        return $query->where('is_active', true);
    }

    public function scopeOrdered(Builder $query): Builder
    {
        return $query->orderBy('sort_order')->orderBy('name');
    }

    // Accessors
    public function getDimensionsAttribute(): string
    {
        return $this->width_mm . 'mm × ' . $this->height_mm . 'mm';
    }

    public function getDisplayNameAttribute(): string
    {
        return $this->name . ' (' . $this->dimensions . ')';
    }

    public function getAreaSqmAttribute(): float
    {
        return ($this->width_mm * $this->height_mm) / 1000000; // Convert mm² to m²
    }
}
