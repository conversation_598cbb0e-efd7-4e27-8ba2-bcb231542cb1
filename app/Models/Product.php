<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Support\Str;

class Product extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'slug',
        'description',
        'category',
        'specifications',
        'production_methods',
        'requires_binding',
        'requires_folding',
        'requires_cutting',
        'min_pages',
        'max_pages',
        'setup_cost_multiplier',
        'is_active',
        'sort_order',
    ];

    protected $casts = [
        'specifications' => 'array',
        'production_methods' => 'array',
        'requires_binding' => 'boolean',
        'requires_folding' => 'boolean',
        'requires_cutting' => 'boolean',
        'is_active' => 'boolean',
        'setup_cost_multiplier' => 'decimal:2',
    ];

    protected static function boot()
    {
        parent::boot();

        static::creating(function ($product) {
            if (empty($product->slug)) {
                $product->slug = Str::slug($product->name);
            }
        });
    }

    public function estimateParts(): HasMany
    {
        return $this->hasMany(EstimatePart::class);
    }

    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    public function scopeByCategory($query, $category)
    {
        return $query->where('category', $category);
    }

    public function scopeOrdered($query)
    {
        return $query->orderBy('sort_order')->orderBy('name');
    }

    public function getDisplayNameAttribute(): string
    {
        return $this->name;
    }

    public function getCategoryLabelAttribute(): string
    {
        return match($this->category) {
            'flat' => 'Flat Products',
            'folded' => 'Folded Products',
            'bound' => 'Bound Products',
            'specialty' => 'Specialty Products',
            default => 'Unknown',
        };
    }

    public function isCompatibleWithProductionMethod($productionMethodId): bool
    {
        if (!$this->production_methods) {
            return true; // If no restrictions, compatible with all
        }

        return in_array($productionMethodId, $this->production_methods);
    }

    public function calculateSetupCost($baseCost): float
    {
        return $baseCost * $this->setup_cost_multiplier;
    }
}
