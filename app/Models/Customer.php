<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Builder;

class Customer extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'company_number',
        'vat_number',
        'description',
        'website',
        'status',
        'credit_limit',
        'payment_terms',
        'notes',
    ];

    protected $casts = [
        'credit_limit' => 'decimal:2',
    ];

    // Relationships
    public function contacts(): HasMany
    {
        return $this->hasMany(CustomerContact::class);
    }

    public function addresses(): HasMany
    {
        return $this->hasMany(CustomerAddress::class);
    }

    public function estimates(): HasMany
    {
        return $this->hasMany(Estimate::class);
    }

    // Scopes
    public function scopeActive(Builder $query): Builder
    {
        return $query->where('status', 'active');
    }

    public function scopeProspects(Builder $query): Builder
    {
        return $query->where('status', 'prospect');
    }

    // Accessors
    public function getPrimaryContactAttribute(): ?CustomerContact
    {
        return $this->contacts()->where('is_primary', true)->first();
    }

    public function getDefaultAddressAttribute(): ?CustomerAddress
    {
        return $this->addresses()->where('is_default', true)->first();
    }

    public function getDisplayNameAttribute(): string
    {
        return $this->name;
    }

    // Helper methods
    public function getPaymentTermsLabelAttribute(): string
    {
        return match($this->payment_terms) {
            'immediate' => 'Immediate',
            '7_days' => '7 Days',
            '14_days' => '14 Days',
            '30_days' => '30 Days',
            '60_days' => '60 Days',
            '90_days' => '90 Days',
            default => '30 Days',
        };
    }

    public function getStatusLabelAttribute(): string
    {
        return match($this->status) {
            'active' => 'Active',
            'inactive' => 'Inactive',
            'prospect' => 'Prospect',
            default => 'Active',
        };
    }
}
