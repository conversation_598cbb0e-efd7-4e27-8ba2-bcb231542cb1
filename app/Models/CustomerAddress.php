<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class CustomerAddress extends Model
{
    use HasFactory;

    protected $fillable = [
        'customer_id',
        'type',
        'label',
        'company_name',
        'address_line_1',
        'address_line_2',
        'city',
        'county',
        'postcode',
        'country',
        'is_default',
        'delivery_instructions',
    ];

    protected $casts = [
        'is_default' => 'boolean',
    ];

    // Relationships
    public function customer(): BelongsTo
    {
        return $this->belongsTo(Customer::class);
    }

    // Accessors
    public function getFullAddressAttribute(): string
    {
        $parts = array_filter([
            $this->company_name,
            $this->address_line_1,
            $this->address_line_2,
            $this->city,
            $this->county,
            $this->postcode,
            $this->country !== 'United Kingdom' ? $this->country : null,
        ]);

        return implode(', ', $parts);
    }

    public function getDisplayNameAttribute(): string
    {
        if ($this->label) {
            return $this->label . ' - ' . $this->address_line_1 . ', ' . $this->city;
        }

        return $this->address_line_1 . ', ' . $this->city;
    }

    public function getTypeLabelAttribute(): string
    {
        return match($this->type) {
            'billing' => 'Billing',
            'delivery' => 'Delivery',
            'both' => 'Billing & Delivery',
            default => 'Delivery',
        };
    }

    // Helper methods
    public function makeDefault(): void
    {
        // Remove default status from other addresses of the same type
        $this->customer->addresses()
            ->where('id', '!=', $this->id)
            ->where('type', $this->type)
            ->update(['is_default' => false]);

        // Set this address as default
        $this->update(['is_default' => true]);
    }
}
