<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;

class ProductionMethod extends Model
{
    protected $fillable = [
        'name',
        'slug',
        'description',
        'setup_cost',
        'is_active',
        'capabilities',
    ];

    protected $casts = [
        'setup_cost' => 'decimal:2',
        'is_active' => 'boolean',
        'capabilities' => 'array',
    ];

    public function machines(): HasMany
    {
        return $this->hasMany(Machine::class);
    }

    public function estimates(): HasMany
    {
        return $this->hasMany(Estimate::class);
    }

    public function activeMachines(): HasMany
    {
        return $this->machines()->where('is_active', true);
    }

    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }
}
