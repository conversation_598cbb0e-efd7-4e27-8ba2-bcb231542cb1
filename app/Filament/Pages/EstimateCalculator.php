<?php

namespace App\Filament\Pages;

use App\Models\Customer;
use App\Models\CustomerContact;
use App\Models\Material;
use App\Models\Size;
use App\Models\Estimate;
use App\Services\EstimateService;
use App\Services\ImpositionService;
use App\Services\ProductionOptionService;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Pages\Page;
use Filament\Forms\Concerns\InteractsWithForms;
use Filament\Forms\Contracts\HasForms;
use Filament\Notifications\Notification;
use Illuminate\Support\Collection;

class EstimateCalculator extends Page implements HasForms
{
    use InteractsWithForms;

    protected static ?string $navigationIcon = 'heroicon-o-calculator';

    protected static ?string $navigationGroup = 'Estimating';

    protected static ?string $navigationLabel = 'Create Estimate';

    protected static ?int $navigationSort = 1;

    protected static string $view = 'filament.pages.estimate-calculator';

    // Form data
    public ?array $data = [];

    // Calculation results
    public ?array $calculation = null;
    public ?array $imposition = null;
    public ?array $productionOptions = null;
    public ?array $selectedProductionOption = null;

    // UI state
    public bool $showResults = false;
    public bool $showVisualization = false;
    public string $optimizationMode = 'optimized';

    public function mount(): void
    {
        $this->form->fill([
            'quantity' => 1000,
            'markup_percentage' => 25,
            'auto_production_setup' => true,
            'version_layout_mode' => 'ganged',
        ]);
    }

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make('Customer Information')
                    ->schema([
                        Forms\Components\Grid::make(2)
                            ->schema([
                                Forms\Components\Select::make('customer_id')
                                    ->label('Customer')
                                    ->options(Customer::active()->pluck('name', 'id'))
                                    ->searchable()
                                    ->preload()
                                    ->reactive()
                                    ->afterStateUpdated(fn ($state, callable $set) => $set('customer_contact_id', null)),
                                Forms\Components\Select::make('customer_contact_id')
                                    ->label('Contact')
                                    ->options(function (callable $get) {
                                        $customerId = $get('customer_id');
                                        if (!$customerId) return [];
                                        return CustomerContact::where('customer_id', $customerId)
                                            ->get()
                                            ->mapWithKeys(fn ($contact) => [$contact->id => "{$contact->full_name} ({$contact->job_title})"]);
                                    })
                                    ->searchable()
                                    ->preload(),
                            ]),
                    ]),

                Forms\Components\Section::make('Job Details')
                    ->schema([
                        Forms\Components\Grid::make(3)
                            ->schema([
                                Forms\Components\TextInput::make('job_title')
                                    ->label('Job Title')
                                    ->required()
                                    ->placeholder('e.g., A5 Flyers'),
                                Forms\Components\TextInput::make('quantity')
                                    ->label('Quantity')
                                    ->required()
                                    ->numeric()
                                    ->default(1000)
                                    ->minValue(1)
                                    ->reactive(),
                                Forms\Components\TextInput::make('markup_percentage')
                                    ->label('Markup (%)')
                                    ->numeric()
                                    ->default(25)
                                    ->reactive(),
                            ]),
                    ]),

                Forms\Components\Section::make('Material & Finished Size')
                    ->schema([
                        Forms\Components\Select::make('material_id')
                            ->label('Material (Brand + Grammage)')
                            ->options(Material::active()->get()->pluck('name', 'id'))
                            ->required()
                            ->searchable()
                            ->preload()
                            ->reactive()
                            ->helperText('Select material brand and weight (e.g., PoshPaper 130gsm)'),

                        Forms\Components\Grid::make(3)
                            ->schema([
                                Forms\Components\Select::make('finished_size_preset')
                                    ->label('Common Size')
                                    ->options(Size::active()->ordered()->get()->pluck('display_name', 'id'))
                                    ->searchable()
                                    ->reactive()
                                    ->afterStateUpdated(function ($state, callable $set) {
                                        if ($state) {
                                            $size = Size::find($state);
                                            if ($size) {
                                                $set('finished_width', $size->width_mm);
                                                $set('finished_height', $size->height_mm);
                                            }
                                        }
                                    })
                                    ->helperText('Select a common size or enter custom dimensions'),
                                Forms\Components\TextInput::make('finished_width')
                                    ->label('Finished Width (mm)')
                                    ->required()
                                    ->numeric()
                                    ->default(210)
                                    ->reactive(),
                                Forms\Components\TextInput::make('finished_height')
                                    ->label('Finished Height (mm)')
                                    ->required()
                                    ->numeric()
                                    ->default(297)
                                    ->reactive(),
                            ]),

                        Forms\Components\Grid::make(2)
                            ->schema([
                                Forms\Components\TextInput::make('bleed')
                                    ->label('Bleed (mm)')
                                    ->numeric()
                                    ->default(3)
                                    ->reactive()
                                    ->helperText('Bleed around finished size'),
                                Forms\Components\TextInput::make('gutter')
                                    ->label('Gutter (mm)')
                                    ->numeric()
                                    ->default(3)
                                    ->reactive()
                                    ->helperText('Space between items on sheet'),
                            ]),
                    ]),

                Forms\Components\Section::make('Production Setup')
                    ->schema([
                        Forms\Components\Toggle::make('auto_production_setup')
                            ->label('Automatic Production Setup')
                            ->default(true)
                            ->reactive()
                            ->helperText('Let the system choose the best machine and material size combination'),

                        Forms\Components\Select::make('version_layout_mode')
                            ->label('Layout Optimization')
                            ->options([
                                'ganged' => 'Optimized (Ganged)',
                                'separate' => 'Unoptimized (Separate)',
                            ])
                            ->default('ganged')
                            ->reactive()
                            ->afterStateUpdated(fn () => $this->updateVisualization())
                            ->helperText('Choose how multiple versions should be laid out'),
                    ])
                    ->visible(fn (callable $get) => $get('material_id')),
            ])
            ->statePath('data');
    }

    public function calculate(): void
    {
        $data = $this->form->getState();

        try {
            if ($data['auto_production_setup'] ?? false) {
                // Auto mode - calculate all production options
                $productionOptionService = app(ProductionOptionService::class);

                // Map form field names to service expected names
                $serviceData = array_merge($data, [
                    'item_width' => $data['finished_width'],
                    'item_height' => $data['finished_height'],
                ]);

                $this->productionOptions = $productionOptionService->calculateAllProductionOptions($serviceData);

                if (empty($this->productionOptions)) {
                    Notification::make()
                        ->title('No viable production options found')
                        ->body('No machines or material sizes can accommodate this job specification.')
                        ->warning()
                        ->send();
                    return;
                }

                // Auto-select the best option (first one, as they're sorted by gross margin)
                $this->selectedProductionOption = $this->productionOptions[0];
                $this->calculation = $this->selectedProductionOption['calculation'];
                $this->imposition = $this->calculation['imposition'];

                Notification::make()
                    ->title('Calculation complete')
                    ->body('Found ' . count($this->productionOptions) . ' production option(s). Best option auto-selected.')
                    ->success()
                    ->send();
            } else {
                // Manual mode - calculate with current settings
                $estimateService = app(EstimateService::class);

                // Map form field names to service expected names
                $serviceData = array_merge($data, [
                    'item_width' => $data['finished_width'],
                    'item_height' => $data['finished_height'],
                ]);

                $this->calculation = $estimateService->calculateEstimate($serviceData);
                $this->imposition = $this->calculation['imposition'];

                Notification::make()
                    ->title('Estimate calculated')
                    ->body('Manual calculation completed successfully.')
                    ->success()
                    ->send();
            }

            $this->showResults = true;
            $this->showVisualization = true;

        } catch (\Exception $e) {
            Notification::make()
                ->title('Calculation Error')
                ->body($e->getMessage())
                ->danger()
                ->send();
        }
    }

    public function selectProductionOption(string $optionId): void
    {
        if (!$this->productionOptions) {
            return;
        }

        $selectedOption = collect($this->productionOptions)->firstWhere('id', $optionId);

        if ($selectedOption) {
            $this->selectedProductionOption = $selectedOption;
            $this->calculation = $selectedOption['calculation'];
            $this->imposition = $this->calculation['imposition'];

            Notification::make()
                ->title('Production option selected!')
                ->body('Using ' . $selectedOption['machine']->name . ' with ' . $selectedOption['material_size']->display_name)
                ->success()
                ->send();
        }
    }

    public function updateVisualization(): void
    {
        if (!$this->calculation) {
            return;
        }

        $data = $this->form->getState();

        // Update the layout mode based on optimization setting
        if ($this->optimizationMode === 'unoptimized') {
            $data['version_layout_mode'] = 'separate';
        } else {
            $data['version_layout_mode'] = 'ganged';
        }

        try {
            if ($data['auto_production_setup'] ?? false) {
                // Recalculate with new optimization mode
                $productionOptionService = app(ProductionOptionService::class);

                // Map form field names to service expected names
                $serviceData = array_merge($data, [
                    'item_width' => $data['finished_width'],
                    'item_height' => $data['finished_height'],
                ]);

                $this->productionOptions = $productionOptionService->calculateAllProductionOptions($serviceData);

                if (!empty($this->productionOptions)) {
                    $this->selectedProductionOption = $this->productionOptions[0];
                    $this->calculation = $this->selectedProductionOption['calculation'];
                    $this->imposition = $this->calculation['imposition'];
                }
            } else {
                // Manual mode - recalculate with new layout mode
                $estimateService = app(EstimateService::class);

                // Map form field names to service expected names
                $serviceData = array_merge($data, [
                    'item_width' => $data['finished_width'],
                    'item_height' => $data['finished_height'],
                ]);

                $this->calculation = $estimateService->calculateEstimate($serviceData);
                $this->imposition = $this->calculation['imposition'];
            }
        } catch (\Exception $e) {
            // Silently fail to avoid disrupting the UI
        }
    }

    public function saveEstimate(): void
    {
        if (!$this->calculation) {
            Notification::make()
                ->title('No calculation to save')
                ->body('Please calculate an estimate first.')
                ->warning()
                ->send();
            return;
        }

        $data = $this->form->getState();

        try {
            $estimate = Estimate::create([
                'customer_id' => $data['customer_id'] ?? null,
                'customer_contact_id' => $data['customer_contact_id'] ?? null,
                'job_title' => $data['job_title'],
                'quantity' => $data['quantity'],
                'finished_width' => $data['finished_width'],
                'finished_height' => $data['finished_height'],
                'bleed' => $data['bleed'] ?? 3,
                'gutter' => $data['gutter'] ?? 3,
                'material_id' => $data['material_id'],
                'machine_id' => $this->selectedProductionOption['machine']->id ?? null,
                'material_size_id' => $this->selectedProductionOption['material_size']->id ?? null,
                'sheet_width' => $this->calculation['sheet_width'],
                'sheet_height' => $this->calculation['sheet_height'],
                'material_cost' => $this->calculation['costs']['material_cost'],
                'production_cost' => $this->calculation['costs']['production_cost'],
                'total_cost' => $this->calculation['costs']['total_cost'],
                'markup_percentage' => $data['markup_percentage'],
                'final_price' => $this->calculation['costs']['final_price'],
                'imposition_layout' => $this->calculation['imposition'],
                'status' => 'draft',
            ]);

            Notification::make()
                ->title('Estimate saved successfully!')
                ->body('Estimate #' . $estimate->id . ' has been created.')
                ->success()
                ->send();

            // Redirect to the estimate view page
            $this->redirect(route('filament.admin.resources.estimates.view', $estimate));

        } catch (\Exception $e) {
            Notification::make()
                ->title('Error saving estimate')
                ->body($e->getMessage())
                ->danger()
                ->send();
        }
    }

    public function resetCalculation(): void
    {
        $this->calculation = null;
        $this->imposition = null;
        $this->productionOptions = null;
        $this->selectedProductionOption = null;
        $this->showResults = false;
        $this->showVisualization = false;
    }
}
