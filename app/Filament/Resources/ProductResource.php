<?php

namespace App\Filament\Resources;

use App\Filament\Resources\ProductResource\Pages;
use App\Filament\Resources\ProductResource\RelationManagers;
use App\Models\Product;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class ProductResource extends Resource
{
    protected static ?string $model = Product::class;

    protected static ?string $navigationIcon = 'heroicon-o-cube';

    protected static ?string $navigationGroup = 'Setup';

    protected static ?int $navigationSort = 1;

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make('Product Information')
                    ->schema([
                        Forms\Components\Grid::make(2)
                            ->schema([
                                Forms\Components\TextInput::make('name')
                                    ->label('Product Name')
                                    ->required()
                                    ->maxLength(255),
                                Forms\Components\Select::make('category')
                                    ->label('Category')
                                    ->options([
                                        'flat' => 'Flat Products',
                                        'folded' => 'Folded Products',
                                        'bound' => 'Bound Products',
                                        'specialty' => 'Specialty Products',
                                    ])
                                    ->required(),
                            ]),
                        Forms\Components\Textarea::make('description')
                            ->label('Description')
                            ->rows(3)
                            ->columnSpanFull(),
                    ]),

                Forms\Components\Section::make('Product Requirements')
                    ->schema([
                        Forms\Components\Grid::make(3)
                            ->schema([
                                Forms\Components\Toggle::make('requires_cutting')
                                    ->label('Requires Cutting')
                                    ->default(true),
                                Forms\Components\Toggle::make('requires_folding')
                                    ->label('Requires Folding'),
                                Forms\Components\Toggle::make('requires_binding')
                                    ->label('Requires Binding'),
                            ]),
                        Forms\Components\Grid::make(3)
                            ->schema([
                                Forms\Components\TextInput::make('min_pages')
                                    ->label('Minimum Pages')
                                    ->numeric()
                                    ->minValue(1),
                                Forms\Components\TextInput::make('max_pages')
                                    ->label('Maximum Pages')
                                    ->numeric()
                                    ->minValue(1),
                                Forms\Components\TextInput::make('setup_cost_multiplier')
                                    ->label('Setup Cost Multiplier')
                                    ->numeric()
                                    ->step(0.1)
                                    ->default(1.0)
                                    ->minValue(0.1),
                            ]),
                    ]),

                Forms\Components\Section::make('Settings')
                    ->schema([
                        Forms\Components\Grid::make(2)
                            ->schema([
                                Forms\Components\Toggle::make('is_active')
                                    ->label('Active')
                                    ->default(true),
                                Forms\Components\TextInput::make('sort_order')
                                    ->label('Sort Order')
                                    ->numeric()
                                    ->default(0),
                            ]),
                    ]),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('name')
                    ->label('Product Name')
                    ->searchable()
                    ->sortable(),
                Tables\Columns\BadgeColumn::make('category')
                    ->label('Category')
                    ->colors([
                        'primary' => 'flat',
                        'success' => 'folded',
                        'warning' => 'bound',
                        'danger' => 'specialty',
                    ])
                    ->formatStateUsing(fn (string $state): string => match ($state) {
                        'flat' => 'Flat',
                        'folded' => 'Folded',
                        'bound' => 'Bound',
                        'specialty' => 'Specialty',
                        default => $state,
                    }),
                Tables\Columns\IconColumn::make('requires_cutting')
                    ->label('Cutting')
                    ->boolean(),
                Tables\Columns\IconColumn::make('requires_folding')
                    ->label('Folding')
                    ->boolean(),
                Tables\Columns\IconColumn::make('requires_binding')
                    ->label('Binding')
                    ->boolean(),
                Tables\Columns\TextColumn::make('setup_cost_multiplier')
                    ->label('Setup Multiplier')
                    ->numeric(decimalPlaces: 2)
                    ->sortable(),
                Tables\Columns\IconColumn::make('is_active')
                    ->label('Active')
                    ->boolean(),
                Tables\Columns\TextColumn::make('sort_order')
                    ->label('Sort Order')
                    ->sortable(),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('category')
                    ->options([
                        'flat' => 'Flat Products',
                        'folded' => 'Folded Products',
                        'bound' => 'Bound Products',
                        'specialty' => 'Specialty Products',
                    ]),
                Tables\Filters\TernaryFilter::make('is_active')
                    ->label('Active Status'),
            ])
            ->actions([
                Tables\Actions\ViewAction::make(),
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ])
            ->defaultSort('sort_order');
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListProducts::route('/'),
            'create' => Pages\CreateProduct::route('/create'),
            'edit' => Pages\EditProduct::route('/{record}/edit'),
        ];
    }
}
