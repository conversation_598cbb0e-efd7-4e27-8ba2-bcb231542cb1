<?php

namespace App\Filament\Resources\EstimateResource\Pages;

use App\Filament\Resources\EstimateResource;
use App\Services\EstimateService;

use Filament\Actions;
use Filament\Forms\Components\Grid;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\Textarea;
use Filament\Forms\Components\Section;
use Filament\Forms\Components\Actions as FormActions;
use Filament\Forms\Components\Actions\Action;
use Filament\Forms\Form;
use Filament\Notifications\Notification;
use Filament\Resources\Pages\EditRecord;
use Filament\Forms\Components\Toggle;

class EditEstimate extends EditRecord
{
    protected static string $resource = EstimateResource::class;

    protected static ?string $title = 'Edit & Recalculate Estimate';

    public ?array $calculation = null;
    public ?array $imposition = null;
    public ?array $productionOptions = null;
    public ?array $selectedProductionOption = null;
    public string $optimizationMode = 'optimized';

    protected function getHeaderActions(): array
    {
        return [
            Actions\ViewAction::make(),
            Actions\DeleteAction::make(),
        ];
    }

    public function mount(int|string $record): void
    {
        parent::mount($record);

        // Pre-populate calculation data from the estimate
        if ($this->record->imposition_layout) {
            $this->calculation = [
                'imposition' => $this->record->imposition_layout,
                'costs' => [
                    'material_cost' => $this->record->material_cost,
                    'production_cost' => $this->record->production_cost,
                    'setup_cost' => $this->record->setup_cost,
                    'total_cost' => $this->record->total_cost,
                    'markup_percentage' => $this->record->markup_percentage,
                    'final_price' => $this->record->final_price,
                ],
                'breakdown' => [
                    'cost_per_item' => $this->record->total_quantity > 0 ? $this->record->final_price / $this->record->total_quantity : 0,
                    'production_time_hours' => $this->record->machine ? $this->record->machine->calculateProductionTime($this->record->sheets_required) : 0,
                ],
            ];
            $this->imposition = $this->record->imposition_layout;

            // Set optimization mode based on existing data
            if (isset($this->imposition['layout_mode'])) {
                $this->optimizationMode = $this->imposition['layout_mode'] === 'separate' ? 'unoptimized' : 'optimized';
            }
        }
    }

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                Section::make('Job Details')
                    ->schema([
                        Select::make('customer_id')
                            ->label('Customer')
                            ->relationship('customer', 'name')
                            ->required()
                            ->searchable()
                            ->preload()
                            ->reactive()
                            ->afterStateUpdated(fn ($state, callable $set) => $set('customer_contact_id', null)),
                        Select::make('customer_contact_id')
                            ->label('Contact')
                            ->options(function (callable $get) {
                                $customerId = $get('customer_id');
                                if (!$customerId) return [];
                                return \App\Models\CustomerContact::where('customer_id', $customerId)
                                    ->get()
                                    ->mapWithKeys(fn ($contact) => [$contact->id => $contact->full_name . ' (' . $contact->job_title . ')']);
                            })
                            ->required()
                            ->reactive()
                            ->afterStateUpdated(function ($state, callable $set) {
                                if ($state) {
                                    $contact = \App\Models\CustomerContact::find($state);
                                    if ($contact) {
                                        $set('client_name', $contact->full_name);
                                    }
                                }
                            }),
                        Grid::make(2)
                            ->schema([
                                TextInput::make('client_name')
                                    ->label('Client Name')
                                    ->required()
                                    ->disabled()
                                    ->dehydrated(),
                                TextInput::make('job_title')
                                    ->label('Job Title')
                                    ->required(),
                            ]),
                        Textarea::make('description')
                            ->label('Description')
                            ->columnSpanFull(),
                    ]),

                Section::make('Item Specifications')
                    ->schema([
                        Grid::make(3)
                            ->schema([
                                TextInput::make('quantity')
                                    ->label('Quantity')
                                    ->numeric()
                                    ->required()
                                    ->hidden(fn ($record) => $record->has_versions),
                                TextInput::make('item_width')
                                    ->label('Item Width (mm)')
                                    ->numeric()
                                    ->required(),
                                TextInput::make('item_height')
                                    ->label('Item Height (mm)')
                                    ->numeric()
                                    ->required(),
                            ]),
                    ]),

                Section::make('Versions')
                    ->schema([
                        \Filament\Forms\Components\Repeater::make('versions')
                            ->label('Version Details')
                            ->schema([
                                Grid::make(2)
                                    ->schema([
                                        TextInput::make('name')
                                            ->label('Version Name')
                                            ->required(),
                                        TextInput::make('quantity')
                                            ->label('Quantity')
                                            ->numeric()
                                            ->required(),
                                    ]),
                            ])
                            ->visible(fn ($record) => $record->has_versions)
                            ->addActionLabel('Add Version')
                            ->collapsible()
                            ->itemLabel(fn (array $state): ?string => $state['name'] ?? 'Version'),


                    ])
                    ->visible(fn ($record) => $record->has_versions),

                Section::make('Production Setup')
                    ->schema([
                        Toggle::make('auto_production_setup')
                            ->label('Automatic Production Setup')
                            ->default(false)
                            ->reactive()
                            ->helperText('Automatically find the best production options, or manually select specific machines'),

                        Select::make('production_method_id')
                            ->label('Production Method')
                            ->options(\App\Models\ProductionMethod::active()->pluck('name', 'id'))
                            ->required()
                            ->reactive()
                            ->visible(fn (callable $get) => !$get('auto_production_setup'))
                            ->afterStateUpdated(fn ($state, callable $set) => $set('machine_id', null)),

                        Select::make('machine_id')
                            ->label('Machine')
                            ->options(function (callable $get) {
                                $methodId = $get('production_method_id');
                                if (!$methodId) return [];
                                return \App\Models\Machine::where('production_method_id', $methodId)
                                    ->where('is_active', true)
                                    ->pluck('name', 'id');
                            })
                            ->required()
                            ->visible(fn (callable $get) => !$get('auto_production_setup'))
                            ->reactive(),

                        Select::make('material_id')
                            ->label('Material')
                            ->options(\App\Models\Material::active()->get()->mapWithKeys(fn ($material) => [$material->id => $material->display_name]))
                            ->required()
                            ->reactive(),
                    ]),

                Section::make('Sheet/Roll Specifications')
                    ->schema([
                        Grid::make(2)
                            ->schema([
                                TextInput::make('sheet_width')
                                    ->label('Sheet/Roll Width (mm)')
                                    ->numeric()
                                    ->required(),
                                TextInput::make('sheet_height')
                                    ->label('Sheet/Roll Height (mm)')
                                    ->numeric()
                                    ->required(),
                            ]),
                        Grid::make(2)
                            ->schema([
                                TextInput::make('markup_percentage')
                                    ->label('Markup (%)')
                                    ->numeric()
                                    ->default(25),
                                Select::make('status')
                                    ->options([
                                        'draft' => 'Draft',
                                        'sent' => 'Sent',
                                        'approved' => 'Approved',
                                        'rejected' => 'Rejected',
                                    ])
                                    ->required(),
                            ]),
                    ]),

                FormActions::make([
                    Action::make('recalculate')
                        ->label('Recalculate Estimate')
                        ->action('recalculateEstimate')
                        ->color('primary')
                        ->size('lg'),
                ])
                ->alignment('center'),
            ]);
    }

    public function recalculateEstimate(): void
    {
        $data = $this->form->getState();

        try {
            if ($data['auto_production_setup'] ?? false) {
                // Calculate all production options automatically
                $productionOptionService = app(\App\Services\ProductionOptionService::class);
                $this->productionOptions = $productionOptionService->calculateAllProductionOptions($data);

                if (!empty($this->productionOptions)) {
                    // Use the best option for the main calculation
                    $bestOption = $this->productionOptions[0];
                    $this->selectedProductionOption = $bestOption;
                    $this->calculation = $bestOption['calculation'];
                    $this->imposition = $this->calculation['imposition'];

                    // Update the record with new calculations
                    $this->record->update([
                        'production_method_id' => $bestOption['machine']->production_method_id,
                        'machine_id' => $bestOption['machine']->id,
                        'material_size_id' => $bestOption['material_size']->id ?? null,
                        'sheet_width' => $bestOption['material_size']->width_mm,
                        'sheet_height' => $bestOption['material_size']->height_mm,
                        'items_per_sheet' => $this->calculation['imposition']['items_per_sheet'],
                        'sheets_required' => $this->calculation['imposition']['sheets_required'],
                        'material_cost' => $this->calculation['costs']['material_cost'],
                        'production_cost' => $this->calculation['costs']['production_cost'],
                        'setup_cost' => $this->calculation['costs']['setup_cost'],
                        'total_cost' => $this->calculation['costs']['total_cost'],
                        'markup_percentage' => $this->calculation['costs']['markup_percentage'],
                        'final_price' => $this->calculation['costs']['final_price'],
                        'imposition_layout' => $this->calculation['imposition'],
                    ]);

                    Notification::make()
                        ->title('Production options calculated!')
                        ->body(count($this->productionOptions) . ' production options found. Best option selected.')
                        ->success()
                        ->send();
                } else {
                    Notification::make()
                        ->title('No compatible production options found')
                        ->body('Try adjusting your specifications or switch to manual mode.')
                        ->warning()
                        ->send();
                }
            } else {
                // Manual mode - use selected production method and machine
                $estimateService = app(EstimateService::class);
                $this->calculation = $estimateService->calculateEstimate($data);
                $this->imposition = $this->calculation['imposition'];
                $this->productionOptions = null;

                // Update the record with new calculations
                $this->record->update([
                    'items_per_sheet' => $this->calculation['imposition']['items_per_sheet'],
                    'sheets_required' => $this->calculation['imposition']['sheets_required'],
                    'material_cost' => $this->calculation['costs']['material_cost'],
                    'production_cost' => $this->calculation['costs']['production_cost'],
                    'setup_cost' => $this->calculation['costs']['setup_cost'],
                    'total_cost' => $this->calculation['costs']['total_cost'],
                    'markup_percentage' => $this->calculation['costs']['markup_percentage'],
                    'final_price' => $this->calculation['costs']['final_price'],
                    'imposition_layout' => $this->calculation['imposition'],
                ]);

                Notification::make()
                    ->title('Estimate recalculated successfully!')
                    ->body('All costs and imposition data have been updated.')
                    ->success()
                    ->send();
            }
        } catch (\Exception $e) {
            Notification::make()
                ->title('Calculation Error')
                ->body($e->getMessage())
                ->danger()
                ->send();
        }
    }

    public function selectProductionOption(string $optionId): void
    {
        if (!$this->productionOptions) {
            return;
        }

        $selectedOption = collect($this->productionOptions)->firstWhere('id', $optionId);

        if ($selectedOption) {
            $this->selectedProductionOption = $selectedOption;
            $this->calculation = $selectedOption['calculation'];
            $this->imposition = $this->calculation['imposition'];

            // Update the record with the selected option
            $this->record->update([
                'production_method_id' => $selectedOption['machine']->production_method_id,
                'machine_id' => $selectedOption['machine']->id,
                'material_size_id' => $selectedOption['material_size']->id ?? null,
                'sheet_width' => $selectedOption['material_size']->width_mm,
                'sheet_height' => $selectedOption['material_size']->height_mm,
                'items_per_sheet' => $this->calculation['imposition']['items_per_sheet'],
                'sheets_required' => $this->calculation['imposition']['sheets_required'],
                'material_cost' => $this->calculation['costs']['material_cost'],
                'production_cost' => $this->calculation['costs']['production_cost'],
                'setup_cost' => $this->calculation['costs']['setup_cost'],
                'total_cost' => $this->calculation['costs']['total_cost'],
                'markup_percentage' => $this->calculation['costs']['markup_percentage'],
                'final_price' => $this->calculation['costs']['final_price'],
                'imposition_layout' => $this->calculation['imposition'],
            ]);

            Notification::make()
                ->title('Production option selected!')
                ->body('Using ' . $selectedOption['machine']->name . ' with ' . $selectedOption['material_size']->display_name)
                ->success()
                ->send();
        }
    }

    public function updateVisualization(): void
    {
        if (!$this->calculation) {
            return;
        }

        $data = $this->form->getState();

        // Update the layout mode based on optimization setting
        if ($this->optimizationMode === 'unoptimized') {
            $data['version_layout_mode'] = 'separate';
        } else {
            $data['version_layout_mode'] = 'ganged';
        }

        try {
            if ($data['auto_production_setup'] ?? false) {
                // Recalculate with new optimization mode
                $productionOptionService = app(\App\Services\ProductionOptionService::class);
                $this->productionOptions = $productionOptionService->calculateAllProductionOptions($data);

                if (!empty($this->productionOptions)) {
                    $bestOption = $this->productionOptions[0];
                    $this->calculation = $bestOption['calculation'];
                    $this->imposition = $this->calculation['imposition'];

                    // Check if a better option is now available
                    if ($this->selectedProductionOption &&
                        $bestOption['final_price'] < $this->selectedProductionOption['final_price']) {
                        Notification::make()
                            ->title('Better option found!')
                            ->body('A more cost-effective option is now available. Consider switching to save £' .
                                   number_format($this->selectedProductionOption['final_price'] - $bestOption['final_price'], 2))
                            ->warning()
                            ->send();
                    }
                }
            } else {
                // Manual mode - recalculate with new layout mode
                $estimateService = app(EstimateService::class);
                $this->calculation = $estimateService->calculateEstimate($data);
                $this->imposition = $this->calculation['imposition'];
            }
        } catch (\Exception $e) {
            // Silently fail to avoid disrupting the UI
        }
    }

    protected function mutateFormDataBeforeFill(array $data): array
    {
        // Extract version layout mode from imposition layout if it exists
        if (isset($data['imposition_layout']['layout_mode'])) {
            $data['version_layout_mode'] = $data['imposition_layout']['layout_mode'];
        }

        return $data;
    }
}
