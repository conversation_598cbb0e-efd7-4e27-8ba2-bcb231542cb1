<?php

namespace App\Filament\Resources;

use App\Filament\Resources\EstimateResource\Pages;
use App\Filament\Resources\EstimateResource\RelationManagers;
use App\Models\Estimate;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Infolists;
use Filament\Infolists\Infolist;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;
use App\Filament\Resources\CustomerResource;

class EstimateResource extends Resource
{
    protected static ?string $model = Estimate::class;

    protected static ?string $navigationIcon = 'heroicon-o-calculator';

    protected static ?string $navigationLabel = 'View Estimates';

    public static function getNavigationBadge(): ?string
    {
        return static::getModel()::count();
    }

    protected static ?string $navigationGroup = 'Estimating';

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('estimate_number')
                    ->searchable(),
                Tables\Columns\TextColumn::make('customer.name')
                    ->label('Customer')
                    ->searchable()
                    ->sortable(),
                Tables\Columns\TextColumn::make('customerContact.full_name')
                    ->label('Contact')
                    ->searchable()
                    ->sortable(),
                Tables\Columns\TextColumn::make('job_title')
                    ->searchable(),
                Tables\Columns\TextColumn::make('productionMethod.name')
                    ->sortable(),
                Tables\Columns\TextColumn::make('total_quantity')
                    ->label('Quantity')
                    ->numeric()
                    ->sortable(),
                Tables\Columns\TextColumn::make('final_price')
                    ->money('GBP')
                    ->sortable(),
                Tables\Columns\TextColumn::make('status')
                    ->badge()
                    ->color(fn (string $state): string => match ($state) {
                        'draft' => 'gray',
                        'sent' => 'warning',
                        'approved' => 'success',
                        'rejected' => 'danger',
                    }),
                Tables\Columns\TextColumn::make('created_at')
                    ->dateTime()
                    ->sortable(),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('status')
                    ->options([
                        'draft' => 'Draft',
                        'sent' => 'Sent',
                        'approved' => 'Approved',
                        'rejected' => 'Rejected',
                    ]),
                Tables\Filters\SelectFilter::make('production_method')
                    ->relationship('productionMethod', 'name'),
            ])
            ->actions([
                Tables\Actions\ViewAction::make()
                    ->label('View Details'),
                Tables\Actions\EditAction::make(),
                Tables\Actions\Action::make('createJob')
                    ->label('Create Job')
                    ->icon('heroicon-o-briefcase')
                    ->color('success')
                    ->action(function ($record) {
                        $job = \App\Models\Job::create([
                            'job_number' => \App\Models\Job::generateJobNumber(),
                            'estimate_id' => $record->id,
                            'customer_id' => $record->customer_id,
                            'customer_contact_id' => $record->customer_contact_id,
                            'client_name' => $record->client_name,
                            'job_title' => $record->job_title,
                            'description' => $record->description,
                            'status' => 'pending',
                            'production_method_id' => $record->production_method_id,
                            'machine_id' => $record->machine_id,
                            'material_id' => $record->material_id,
                            'material_size_id' => $record->material_size_id,
                            'quantity' => $record->quantity,
                            'has_versions' => $record->has_versions,
                            'versions' => $record->versions,
                            'total_quantity' => $record->total_quantity,
                            'item_width' => $record->item_width,
                            'item_height' => $record->item_height,
                            'sheet_width' => $record->sheet_width,
                            'sheet_height' => $record->sheet_height,
                            'items_per_sheet' => $record->items_per_sheet,
                            'sheets_required' => $record->sheets_required,
                            'imposition_layout' => $record->imposition_layout,
                            'material_cost' => $record->material_cost,
                            'production_cost' => $record->production_cost,
                            'setup_cost' => $record->setup_cost,
                            'total_cost' => $record->total_cost,
                            'markup_percentage' => $record->markup_percentage,
                            'final_price' => $record->final_price,
                        ]);

                        $record->update(['status' => 'approved']);

                        \Filament\Notifications\Notification::make()
                            ->title('Job Created Successfully!')
                            ->body('Job ' . $job->job_number . ' has been created.')
                            ->success()
                            ->send();

                        return redirect()->route('filament.admin.resources.job.edit', $job);
                    })
                    ->visible(fn ($record) => $record->status !== 'approved')
                    ->requiresConfirmation()
                    ->modalHeading('Create Job from Estimate')
                    ->modalDescription('This will create a new job and mark the estimate as approved.'),
                Tables\Actions\Action::make('sendEmail')
                    ->label('Send Email')
                    ->icon('heroicon-o-envelope')
                    ->color('info')
                    ->action(function ($record) {
                        try {
                            app(\App\Http\Controllers\PublicEstimateController::class)->email($record);

                            \Filament\Notifications\Notification::make()
                                ->title('Email Sent')
                                ->body('Estimate email sent successfully!')
                                ->success()
                                ->send();
                        } catch (\Exception $e) {
                            \Filament\Notifications\Notification::make()
                                ->title('Email Error')
                                ->body('Error sending email: ' . $e->getMessage())
                                ->danger()
                                ->send();
                        }
                    })
                    ->requiresConfirmation()
                    ->modalHeading('Send Estimate Email')
                    ->modalDescription('This will send the estimate to the customer via email.'),
                Tables\Actions\Action::make('viewImposition')
                    ->label('View Layout')
                    ->icon('heroicon-o-squares-2x2')
                    ->color('info')
                    ->url(fn (Estimate $record): string => static::getUrl('view', ['record' => $record])),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\TextInput::make('estimate_number')
                    ->disabled()
                    ->dehydrated(false),
                Forms\Components\Grid::make(2)
                    ->schema([
                        Forms\Components\TextInput::make('client_name')
                            ->required()
                            ->maxLength(255),
                        Forms\Components\TextInput::make('job_title')
                            ->required()
                            ->maxLength(255),
                    ]),
                Forms\Components\Textarea::make('description')
                    ->columnSpanFull(),
                Forms\Components\Select::make('status')
                    ->options([
                        'draft' => 'Draft',
                        'sent' => 'Sent',
                        'approved' => 'Approved',
                        'rejected' => 'Rejected',
                    ])
                    ->default('draft')
                    ->required(),
            ]);
    }

    public static function infolist(Infolist $infolist): Infolist
    {
        return $infolist
            ->schema([
                Infolists\Components\Section::make('Job Information')
                    ->schema([
                        Infolists\Components\Grid::make(2)
                            ->schema([
                                Infolists\Components\TextEntry::make('estimate_number')
                                    ->label('Estimate Number'),
                                Infolists\Components\TextEntry::make('status')
                                    ->badge()
                                    ->color(fn (string $state): string => match ($state) {
                                        'draft' => 'gray',
                                        'sent' => 'warning',
                                        'approved' => 'success',
                                        'rejected' => 'danger',
                                    }),
                            ]),
                        Infolists\Components\Grid::make(2)
                            ->schema([
                                Infolists\Components\TextEntry::make('customer.name')
                                    ->label('Customer')
                                    ->url(fn ($record) => $record->customer ? CustomerResource::getUrl('edit', ['record' => $record->customer]) : null),
                                Infolists\Components\TextEntry::make('client_name')
                                    ->label('Client Name'),
                            ]),
                        Infolists\Components\TextEntry::make('job_title')
                            ->label('Job Title'),
                        Infolists\Components\TextEntry::make('description')
                            ->columnSpanFull(),
                    ]),

                Infolists\Components\Section::make('Specifications')
                    ->schema([
                        Infolists\Components\Grid::make(3)
                            ->schema([
                                Infolists\Components\TextEntry::make('quantity')
                                    ->numeric()
                                    ->visible(fn ($record) => !$record->has_versions),
                                Infolists\Components\TextEntry::make('total_quantity')
                                    ->label('Total Quantity')
                                    ->numeric()
                                    ->visible(fn ($record) => $record->has_versions),
                                Infolists\Components\TextEntry::make('item_width')
                                    ->label('Item Width')
                                    ->suffix('mm'),
                                Infolists\Components\TextEntry::make('item_height')
                                    ->label('Item Height')
                                    ->suffix('mm'),
                            ]),
                        Infolists\Components\Grid::make(2)
                            ->schema([
                                Infolists\Components\TextEntry::make('sheet_width')
                                    ->label('Sheet Width')
                                    ->suffix('mm'),
                                Infolists\Components\TextEntry::make('sheet_height')
                                    ->label('Sheet Height')
                                    ->suffix('mm'),
                            ]),
                    ]),

                Infolists\Components\Section::make('Versions')
                    ->schema([
                        Infolists\Components\RepeatableEntry::make('versions_list')
                            ->label('')
                            ->schema([
                                Infolists\Components\Grid::make(3)
                                    ->schema([
                                        Infolists\Components\TextEntry::make('name')
                                            ->label('Version Name'),
                                        Infolists\Components\TextEntry::make('quantity')
                                            ->label('Quantity')
                                            ->numeric(),
                                        Infolists\Components\TextEntry::make('percentage')
                                            ->label('Percentage')
                                            ->formatStateUsing(fn ($state) => number_format($state, 1) . '%'),
                                    ]),
                            ])
                            ->contained(false),
                    ])
                    ->visible(fn ($record) => $record->has_versions),

                Infolists\Components\Section::make('Production Setup')
                    ->schema([
                        Infolists\Components\Grid::make(2)
                            ->schema([
                                Infolists\Components\TextEntry::make('productionMethod.name')
                                    ->label('Production Method'),
                                Infolists\Components\TextEntry::make('machine.name')
                                    ->label('Machine'),
                            ]),
                        Infolists\Components\Grid::make(2)
                            ->schema([
                                Infolists\Components\TextEntry::make('material.display_name')
                                    ->label('Material'),
                                Infolists\Components\TextEntry::make('materialSize.display_name')
                                    ->label('Material Size')
                                    ->default('Custom Size'),
                            ]),
                    ]),

                Infolists\Components\Section::make('Imposition Results')
                    ->schema([
                        Infolists\Components\Grid::make(4)
                            ->schema([
                                Infolists\Components\TextEntry::make('items_per_sheet')
                                    ->label('Items per Sheet'),
                                Infolists\Components\TextEntry::make('sheets_required')
                                    ->label('Sheets Required'),
                                Infolists\Components\TextEntry::make('efficiency_percentage')
                                    ->label('Layout Efficiency')
                                    ->suffix('%')
                                    ->formatStateUsing(fn ($state) => number_format($state, 1)),
                                Infolists\Components\TextEntry::make('waste_percentage')
                                    ->label('Waste')
                                    ->suffix('%')
                                    ->formatStateUsing(fn ($record) => number_format(100 - $record->efficiency_percentage, 1)),
                            ]),
                    ]),

                Infolists\Components\Section::make('Cost Breakdown')
                    ->schema([
                        Infolists\Components\Grid::make(2)
                            ->schema([
                                Infolists\Components\TextEntry::make('material_cost')
                                    ->label('Material Cost')
                                    ->money('GBP'),
                                Infolists\Components\TextEntry::make('production_cost')
                                    ->label('Production Cost')
                                    ->money('GBP'),
                                Infolists\Components\TextEntry::make('setup_cost')
                                    ->label('Setup Cost')
                                    ->money('GBP'),
                                Infolists\Components\TextEntry::make('total_cost')
                                    ->label('Total Cost')
                                    ->money('GBP'),
                                Infolists\Components\TextEntry::make('markup_percentage')
                                    ->label('Markup')
                                    ->suffix('%'),
                                Infolists\Components\TextEntry::make('final_price')
                                    ->label('Final Price')
                                    ->money('GBP')
                                    ->size(Infolists\Components\TextEntry\TextEntrySize::Large)
                                    ->weight('bold')
                                    ->color('success'),
                            ]),
                    ]),

                Infolists\Components\Section::make('Imposition Visualisation')
                    ->schema([
                        Infolists\Components\ViewEntry::make('imposition_layout')
                            ->label('')
                            ->view('filament.infolists.imposition-visualization'),
                    ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListEstimates::route('/'),
            'create' => Pages\CreateEstimate::route('/create'),
            'view' => Pages\ViewEstimate::route('/{record}'),
            'edit' => Pages\EditEstimate::route('/{record}/edit'),
        ];
    }
}
