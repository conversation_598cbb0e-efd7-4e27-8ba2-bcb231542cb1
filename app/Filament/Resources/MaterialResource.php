<?php

namespace App\Filament\Resources;

use App\Filament\Resources\MaterialResource\Pages;
use App\Filament\Resources\MaterialResource\RelationManagers;
use App\Models\Material;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Forms\Components\Tabs;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class MaterialResource extends Resource
{
    protected static ?string $model = Material::class;

    protected static ?string $navigationIcon = 'heroicon-o-cube';

    protected static ?string $navigationGroup = 'Ink & Materials';

    protected static ?int $navigationSort = 2;

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Tabs::make('Material Form Tabs')
                    ->tabs([
                        Tabs\Tab::make('Basic Information')
                            ->schema([
                                Forms\Components\Select::make('material_type_id')
                                    ->label('Material Type')
                                    ->relationship('materialType', 'name')
                                    ->required()
                                    ->createOptionForm([
                                        Forms\Components\TextInput::make('name')
                                            ->required()
                                            ->maxLength(255),
                                        Forms\Components\TextInput::make('slug')
                                            ->required()
                                            ->maxLength(255),
                                        Forms\Components\Textarea::make('description')
                                            ->rows(3),
                                    ]),

                                Forms\Components\TextInput::make('name')
                                    ->required()
                                    ->maxLength(255),

                                Forms\Components\Select::make('finish')
                                    ->options([
                                        'gloss' => 'Gloss',
                                        'matt' => 'Matt',
                                        'silk' => 'Silk',
                                        'uncoated' => 'Uncoated',
                                    ]),

                                Forms\Components\TextInput::make('weight')
                                    ->numeric()
                                    ->suffix('gsm')
                                    ->reactive()
                                    ->afterStateUpdated(
                                        fn($state, callable $set, callable $get) =>
                                        static::updateCalculatedFields($set, $get)
                                    ),

                                Forms\Components\TextInput::make('caliper')
                                    ->numeric()
                                    ->suffix('mic'),
                            ]),



                        Tabs\Tab::make('Costing')
                            ->schema([
                                // Sheet costing (cost per ton only)
                                Forms\Components\Grid::make(1)
                                    ->schema([
                                        Forms\Components\TextInput::make('cost_per_ton')
                                            ->label('Cost per Ton')
                                            ->numeric()
                                            ->prefix('£')
                                            ->required(fn($get) => static::isSheetMaterial($get))
                                            ->helperText('Enter cost per ton - costs per sheet and m² will be calculated in Material Sizes'),
                                    ])
                                    ->visible(fn($get) => static::isSheetMaterial($get)),

                                // Roll costing
                                Forms\Components\Grid::make(3)
                                    ->schema([
                                        Forms\Components\TextInput::make('roll_cost')
                                            ->label('Roll Cost')
                                            ->numeric()
                                            ->prefix('£')
                                            ->required(fn($get) => static::isRollMaterial($get))
                                            ->helperText('Total cost for the entire roll'),

                                        Forms\Components\TextInput::make('roll_length_meters')
                                            ->label('Roll Length')
                                            ->numeric()
                                            ->suffix('m')
                                            ->required(fn($get) => static::isRollMaterial($get))
                                            ->helperText('Length of the roll in meters'),

                                        Forms\Components\TextInput::make('roll_width')
                                            ->label('Roll Width')
                                            ->numeric()
                                            ->suffix('mm')
                                            ->required(fn($get) => static::isRollMaterial($get))
                                            ->helperText('Width of the roll in millimeters'),
                                    ])
                                    ->visible(fn($get) => static::isRollMaterial($get)),
                            ]),

                        Tabs\Tab::make('Settings')
                            ->schema([
                                Forms\Components\Toggle::make('is_active')
                                    ->label('Active')
                                    ->required()
                                    ->default(true),

                                Forms\Components\Textarea::make('notes')
                                    ->label('Notes')
                                    ->rows(3)
                                    ->columnSpanFull(),
                            ]),
                    ])
                    ->columnSpan('full'), // Optional: Make tabs span full width
            ]);
    }

    protected static function isSheetMaterial(callable $get): bool
    {
        $materialTypeId = $get('material_type_id');
        if (!$materialTypeId)
            return false;

        $materialType = \App\Models\MaterialType::find($materialTypeId);
        return $materialType?->slug === 'sheet';
    }

    protected static function isRollMaterial(callable $get): bool
    {
        $materialTypeId = $get('material_type_id');
        if (!$materialTypeId)
            return false;

        $materialType = \App\Models\MaterialType::find($materialTypeId);
        return $materialType?->slug === 'roll';
    }



    public static function table(Table $table): Table
    {
        return $table
            ->columns([

                Tables\Columns\TextColumn::make('name')
                    ->searchable(),

                Tables\Columns\TextColumn::make('materialType.name')
                    ->label('Type')
                    ->badge()
                    ->color('primary')
                    ->sortable(),

                Tables\Columns\TextColumn::make('finish')
                    ->formatStateUsing(fn (string $state): string => ucfirst($state)),
                Tables\Columns\TextColumn::make('weight')
                    ->numeric()
                    ->suffix('gsm')
                    ->sortable(),
                Tables\Columns\TextColumn::make('caliper')
                    ->numeric()
                    ->suffix('mic')
                    ->sortable(),
                //Tables\Columns\TextColumn::make('cost_per_sqm')
                //    ->money('GBP')
                //    ->sortable(),
                
                Tables\Columns\IconColumn::make('is_active')
                    ->label('Status')
                    ->boolean(),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('material_type_id')
                    ->label('Material Type')
                    ->relationship('materialType', 'name')
                    ->searchable()
                    ->preload(),

                Tables\Filters\SelectFilter::make('finish')
                    ->label('Finish')
                    ->options(function () {
                        return \App\Models\Material::whereNotNull('finish')
                            ->distinct()
                            ->pluck('finish', 'finish')
                            ->sort();
                    })
                    ->searchable(),

                Tables\Filters\TernaryFilter::make('is_active')
                    ->label('Active Status')
                    ->boolean()
                    ->trueLabel('Active only')
                    ->falseLabel('Inactive only')
                    ->native(false),
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            RelationManagers\SizesRelationManager::class,
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListMaterials::route('/'),
            'create' => Pages\CreateMaterial::route('/create'),
            'edit' => Pages\EditMaterial::route('/{record}/edit'),
        ];
    }
}
