<?php

namespace App\Filament\Resources\InkandTonerResource\Pages;

use App\Filament\Resources\InkandTonerResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;

class ListInkandToners extends ListRecords
{
    protected static string $resource = InkandTonerResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make(),
        ];
    }
}
