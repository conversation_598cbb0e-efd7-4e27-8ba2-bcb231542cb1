<?php

namespace App\Filament\Resources\MaterialResource\RelationManagers;

use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Tables;
use Filament\Tables\Table;

class SizesRelationManager extends RelationManager
{
    protected static string $relationship = 'sizes';

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make('Size Information')
                    ->description('Select an existing size or create a new one')
                    ->schema([
                        Forms\Components\Select::make('id')
                            ->label('Size')
                            ->relationship('', 'name')
                            ->getOptionLabelFromRecordUsing(fn ($record) => $record->display_name)
                            ->searchable()
                            ->preload()
                            ->createOptionForm([
                                Forms\Components\TextInput::make('name')
                                    ->label('Size Name')
                                    ->required()
                                    ->maxLength(255)
                                    ->placeholder('e.g., A4, SRA3, Custom 700x1000'),
                                Forms\Components\Grid::make(2)
                                    ->schema([
                                        Forms\Components\TextInput::make('width_mm')
                                            ->label('Width (mm)')
                                            ->required()
                                            ->numeric()
                                            ->suffix('mm'),
                                        Forms\Components\TextInput::make('height_mm')
                                            ->label('Height (mm)')
                                            ->required()
                                            ->numeric()
                                            ->suffix('mm'),
                                    ]),
                                Forms\Components\TextInput::make('description')
                                    ->label('Description')
                                    ->maxLength(255),
                                Forms\Components\Toggle::make('is_active')
                                    ->label('Active')
                                    ->default(true),
                            ])
                            ->required(),
                    ]),

                Forms\Components\Section::make('Pack Types (Sheet Materials Only)')
                    ->description('Set quantities for different pack types')
                    ->schema([
                        Forms\Components\Grid::make(2)
                            ->schema([
                                Forms\Components\TextInput::make('ream_quantity')
                                    ->label('Ream Quantity')
                                    ->numeric()
                                    ->suffix('sheets')
                                    ->placeholder('e.g., 250, 500')
                                    ->helperText('Number of sheets in a ream wrap'),
                                Forms\Components\TextInput::make('bulk_quantity')
                                    ->label('Bulk Quantity')
                                    ->numeric()
                                    ->suffix('sheets')
                                    ->placeholder('e.g., 8000, 16000')
                                    ->helperText('Number of sheets in a bulk pallet'),
                            ]),
                        Forms\Components\Toggle::make('is_available')
                            ->label('Available')
                            ->default(true),
                    ]),
            ]);
    }

    public function table(Table $table): Table
    {
        return $table
            ->recordTitleAttribute('name')
            ->columns([
                Tables\Columns\TextColumn::make('name')
                    ->label('Size Name')
                    ->searchable()
                    ->sortable(),
                Tables\Columns\TextColumn::make('dimensions')
                    ->label('Dimensions')
                    ->getStateUsing(fn ($record) => $record->dimensions),
                Tables\Columns\TextColumn::make('area_sqm')
                    ->label('Area (m²)')
                    ->getStateUsing(fn ($record) => number_format($record->area_sqm, 4)),
                Tables\Columns\TextColumn::make('pivot.ream_quantity')
                    ->label('Ream Qty')
                    ->numeric()
                    ->sortable(),
                Tables\Columns\TextColumn::make('pivot.bulk_quantity')
                    ->label('Bulk Qty')
                    ->numeric()
                    ->sortable(),
                Tables\Columns\TextColumn::make('cost_per_1000_sheets')
                    ->label('Cost/1000 Sheets')
                    ->money('GBP')
                    ->getStateUsing(function ($record) {
                        $material = $this->getOwnerRecord();
                        if ($material->isSheet() && $material->cost_per_ton && $material->weight) {
                            // Calculate cost per 1000 sheets inline to avoid IDE confusion
                            $sheetAreaSqm = ($record->width_mm * $record->height_mm) / 1000000;
                            $sheetWeightKg = ($sheetAreaSqm * $material->weight) / 1000;
                            $costPerSheet = ($material->cost_per_ton / 1000) * $sheetWeightKg;
                            return $costPerSheet * 1000;
                        }
                        return null;
                    }),
                Tables\Columns\IconColumn::make('pivot.is_available')
                    ->label('Available')
                    ->boolean(),
            ])
            ->filters([
                Tables\Filters\TernaryFilter::make('pivot.is_available')
                    ->label('Available'),
            ])
            ->headerActions([
                Tables\Actions\AttachAction::make()
                    ->form(fn (Tables\Actions\AttachAction $action): array => [
                        $action->getRecordSelect()
                            ->getOptionLabelFromRecordUsing(fn ($record) => $record->display_name),
                        Forms\Components\TextInput::make('ream_quantity')
                            ->label('Ream Quantity')
                            ->numeric()
                            ->suffix('sheets')
                            ->placeholder('e.g., 250, 500'),
                        Forms\Components\TextInput::make('bulk_quantity')
                            ->label('Bulk Quantity')
                            ->numeric()
                            ->suffix('sheets')
                            ->placeholder('e.g., 8000, 16000'),
                        Forms\Components\Toggle::make('is_available')
                            ->label('Available')
                            ->default(true),
                    ]),
                Tables\Actions\CreateAction::make()
                    ->createAnother(false),
            ])
            ->actions([
                Tables\Actions\EditAction::make()
                    ->form([
                        Forms\Components\TextInput::make('ream_quantity')
                            ->label('Ream Quantity')
                            ->numeric()
                            ->suffix('sheets'),
                        Forms\Components\TextInput::make('bulk_quantity')
                            ->label('Bulk Quantity')
                            ->numeric()
                            ->suffix('sheets'),
                        Forms\Components\Toggle::make('is_available')
                            ->label('Available'),
                    ]),
                Tables\Actions\DetachAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DetachBulkAction::make(),
                ]),
            ])
            ->defaultSort('name');
    }
}
