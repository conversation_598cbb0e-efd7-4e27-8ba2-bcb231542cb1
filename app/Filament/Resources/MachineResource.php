<?php

namespace App\Filament\Resources;

use App\Filament\Resources\MachineResource\Pages;
use App\Filament\Resources\MachineResource\RelationManagers;
use App\Models\Machine;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class MachineResource extends Resource
{
    protected static ?string $model = Machine::class;

    protected static ?string $navigationIcon = 'heroicon-o-wrench-screwdriver';

    protected static ?string $navigationGroup = 'Print Setup';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Select::make('production_method_id')
                    ->relationship('productionMethod', 'name')
                    ->required(),
                Forms\Components\TextInput::make('name')
                    ->required()
                    ->maxLength(255),
                Forms\Components\Select::make('type')
                    ->options([
                        'sheet-fed' => 'Sheet-fed',
                        'roll-fed' => 'Roll-fed',
                    ])
                    ->required()
                    ->default('sheet-fed'),
                Forms\Components\TextInput::make('model')
                    ->maxLength(255),
                Forms\Components\Grid::make(2)
                    ->schema([
                        Forms\Components\TextInput::make('hourly_rate')
                            ->required()
                            ->numeric()
                            ->prefix('£'),
                        Forms\Components\TextInput::make('setup_cost')
                            ->required()
                            ->numeric()
                            ->prefix('£')
                            ->default(0),
                    ]),
                Forms\Components\Grid::make(2)
                    ->schema([
                        Forms\Components\TextInput::make('max_size_width')
                            ->label('Max Sheet Width')
                            ->numeric()
                            ->suffix('mm'),
                        Forms\Components\TextInput::make('max_size_height')
                            ->label('Max Sheet Height')
                            ->numeric()
                            ->suffix('mm'),
                        Forms\Components\TextInput::make('min_size_width')
                            ->label('Min Sheet Width')
                            ->numeric()
                            ->suffix('mm'),
                        Forms\Components\TextInput::make('min_size_height')
                            ->label('Min Sheet Height')
                            ->numeric()
                            ->suffix('mm'),
                    ]),
                Forms\Components\Grid::make(2)
                    ->schema([
                        Forms\Components\TextInput::make('run_speed')
                            ->label('Run Speed')
                            ->required()
                            ->numeric(),
                        Forms\Components\Select::make('speed_unit')
                            ->label('Speed Unit')
                            ->options([
                                'sheets/hour' => 'Sheets/Hour',
                                'metres/hour' => 'Metres/Hour',
                            ])
                            ->required()
                            ->default('sheets/hour'),
                    ]),
                Forms\Components\TextInput::make('waste_percentage')
                    ->numeric()
                    ->suffix('%')
                    ->default(5),
                Forms\Components\Toggle::make('is_active')
                    ->required()
                    ->default(true),
                Forms\Components\Textarea::make('notes')
                    ->columnSpanFull(),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('productionMethod.name')
                    ->sortable(),
                Tables\Columns\TextColumn::make('name')
                    ->searchable(),
                Tables\Columns\TextColumn::make('model')
                    ->searchable(),
                Tables\Columns\TextColumn::make('hourly_rate')
                    ->money('GBP')
                    ->sortable(),
                Tables\Columns\TextColumn::make('max_size_width')
                    ->label('Max Width')
                    ->numeric()
                    ->suffix('mm')
                    ->sortable(),
                Tables\Columns\TextColumn::make('max_size_height')
                    ->label('Max Height')
                    ->numeric()
                    ->suffix('mm')
                    ->sortable(),
                Tables\Columns\TextColumn::make('run_speed')
                    ->label('Run Speed')
                    ->numeric()
                    ->sortable()
                    ->formatStateUsing(fn ($record) => number_format($record->run_speed) . ' ' . $record->speed_unit),
                Tables\Columns\IconColumn::make('is_active')
                    ->boolean(),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('production_method')
                    ->relationship('productionMethod', 'name'),
                Tables\Filters\TernaryFilter::make('is_active'),
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListMachines::route('/'),
            'create' => Pages\CreateMachine::route('/create'),
            'edit' => Pages\EditMachine::route('/{record}/edit'),
        ];
    }
}
