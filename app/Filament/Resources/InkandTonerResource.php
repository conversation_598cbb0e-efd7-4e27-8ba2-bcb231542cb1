<?php

namespace App\Filament\Resources;

use App\Filament\Resources\InkandTonerResource\Pages;
use App\Filament\Resources\InkandTonerResource\RelationManagers;
use App\Models\InkandToner;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class InkandTonerResource extends Resource
{
    protected static ?string $model = InkandToner::class;

    protected static ?string $navigationIcon = 'heroicon-o-swatch';

    protected static ?string $navigationGroup = 'Print Setup';

    protected static ?string $navigationLabel = 'Ink & Toner';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\TextInput::make('name')
                    ->required()
                    ->maxLength(255),
                Forms\Components\TextInput::make('manufacturer')
                    ->maxLength(255),
                Forms\Components\Select::make('type')
                    ->options([
                        'ink' => 'Ink',
                        'toner' => 'Toner',
                    ])
                    ->required(),
                Forms\Components\Select::make('colour_type')
                    ->options([
                        'cmyk' => 'Process (CMYK)',
                        'pantone' => 'Pantone (PMS)',
                        'metallic' => 'Metallic',
                        'neon' => 'Neon',
                        'other' => 'Other',
                    ])
                    ->required(),
                Forms\Components\TextInput::make('consumption_rate_kg_per_m2')
                    ->label('Consumption Rate (kg per m²)')
                    ->helperText('Ink usage at 100% coverage (e.g. 0.0015 for 1.5g/m²)')
                    ->numeric()
                    ->step(0.000001)
                    ->default(0.0015)
                    ->required(),
                Forms\Components\TextInput::make('cost_per_kg')
                    ->required()
                    ->numeric()
                    ->prefix('£')
                    ->default(0),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('name')
                    ->searchable(),
                Tables\Columns\TextColumn::make('manufacturer')
                    ->searchable(),
                Tables\Columns\TextColumn::make('type')
                    ->badge()
                    ->color(fn(string $state): string => match ($state) {
                        'ink' => 'primary',
                        'toner' => 'secondary',
                        default => 'gray',
                    })
                    ->sortable(),
                Tables\Columns\TextColumn::make('colour_type')
                    ->label('Colour Type')
                    ->sortable(),
                Tables\Columns\TextColumn::make('cost_per_kg')
                    ->money('GBP')
                    ->sortable(),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('type')
                    ->options([
                        'ink' => 'Ink',
                        'toner' => 'Toner',
                    ]),
                Tables\Filters\SelectFilter::make('colour_type')
                    ->options([
                        'cmyk' => 'Process (CMYK)',
                        'pantone' => 'Pantone (PMS)',
                        'metallic' => 'Metallic',
                    ]),
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListInkandToners::route('/'),
            'create' => Pages\CreateInkandToner::route('/create'),
            'edit' => Pages\EditInkandToner::route('/{record}/edit'),
        ];
    }
}