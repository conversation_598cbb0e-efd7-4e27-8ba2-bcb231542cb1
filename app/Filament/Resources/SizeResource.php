<?php

namespace App\Filament\Resources;

use App\Filament\Resources\SizeResource\Pages;
use App\Filament\Resources\SizeResource\RelationManagers;
use App\Models\Size;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class SizeResource extends Resource
{
    protected static ?string $model = Size::class;

    protected static ?string $navigationIcon = 'heroicon-o-squares-2x2';

    protected static ?string $navigationGroup = 'Materials';

    protected static ?string $navigationLabel = 'Sizes';

    protected static ?int $navigationSort = 1;

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make('Size Information')
                    ->schema([
                        Forms\Components\Grid::make(2)
                            ->schema([
                                Forms\Components\TextInput::make('name')
                                    ->label('Size Name')
                                    ->required()
                                    ->maxLength(255)
                                    ->placeholder('e.g., A4, B1, SRA2')
                                    ->helperText('Standard size name (A4, B1, etc.) or custom name'),
                                Forms\Components\TextInput::make('sort_order')
                                    ->label('Sort Order')
                                    ->numeric()
                                    ->default(0)
                                    ->helperText('Lower numbers appear first'),
                            ]),
                    ]),

                Forms\Components\Section::make('Dimensions')
                    ->schema([
                        Forms\Components\Grid::make(2)
                            ->schema([
                                Forms\Components\TextInput::make('width_mm')
                                    ->label('Width (mm)')
                                    ->required()
                                    ->numeric()
                                    ->suffix('mm'),
                                Forms\Components\TextInput::make('height_mm')
                                    ->label('Height (mm)')
                                    ->required()
                                    ->numeric()
                                    ->suffix('mm'),
                            ]),
                        Forms\Components\Placeholder::make('calculated_area')
                            ->label('Calculated Area')
                            ->content(function (callable $get) {
                                $width = $get('width_mm');
                                $height = $get('height_mm');
                                if ($width && $height) {
                                    $sqm = ($width * $height) / 1000000;
                                    return number_format($sqm, 4) . ' m²';
                                }
                                return 'Enter dimensions to calculate area';
                            }),
                    ]),

                Forms\Components\Section::make('Settings')
                    ->schema([
                        Forms\Components\Grid::make(2)
                            ->schema([
                                Forms\Components\Toggle::make('is_active')
                                    ->label('Active')
                                    ->default(true)
                                    ->helperText('Only active sizes will be available for selection'),
                                Forms\Components\Placeholder::make('spacer'),
                            ]),
                        Forms\Components\Textarea::make('description')
                            ->label('Description')
                            ->placeholder('Optional description or notes about this size')
                            ->columnSpanFull(),
                    ]),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('name')
                    ->label('Size Name')
                    ->searchable()
                    ->sortable(),
                Tables\Columns\TextColumn::make('dimensions')
                    ->label('Dimensions')
                    ->getStateUsing(fn ($record) => $record->dimensions),
                Tables\Columns\TextColumn::make('area_sqm')
                    ->label('Area (m²)')
                    ->getStateUsing(fn ($record) => number_format($record->area_sqm, 4))
                    ->sortable(),
                Tables\Columns\TextColumn::make('sort_order')
                    ->label('Sort Order')
                    ->sortable(),
                Tables\Columns\IconColumn::make('is_active')
                    ->label('Active')
                    ->boolean(),
                Tables\Columns\TextColumn::make('description')
                    ->label('Description')
                    ->limit(50)
                    ->tooltip(function (Tables\Columns\TextColumn $column): ?string {
                        $state = $column->getState();
                        if (strlen($state) <= 50) {
                            return null;
                        }
                        return $state;
                    }),
            ])
            ->filters([
                //
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListSizes::route('/'),
            'create' => Pages\CreateSize::route('/create'),
            'edit' => Pages\EditSize::route('/{record}/edit'),
        ];
    }
}
