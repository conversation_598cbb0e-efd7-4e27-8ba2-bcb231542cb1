<?php

namespace App\Services;

use App\Models\Estimate;
use App\Models\Machine;
use App\Models\Material;
use App\Models\ProductionMethod;

class EstimateService
{
    protected ImpositionService $impositionService;

    public function __construct(ImpositionService $impositionService)
    {
        $this->impositionService = $impositionService;
    }

    /**
     * Calculate a complete estimate
     */
    public function calculateEstimate(array $data): array
    {
        $productionMethod = ProductionMethod::findOrFail($data['production_method_id']);
        $machine = Machine::findOrFail($data['machine_id']);
        $material = Material::findOrFail($data['material_id']);

        // Determine if we have versions
        $hasVersions = isset($data['has_versions']) && $data['has_versions'] && !empty($data['versions']);
        $quantity = $hasVersions ? collect($data['versions'])->sum('quantity') : $data['quantity'];

        // Calculate imposition
        if ($hasVersions) {
            $imposition = $this->impositionService->calculateImpositionWithVersions(
                $data['item_width'],
                $data['item_height'],
                $data['sheet_width'],
                $data['sheet_height'],
                $data['versions'],
                $data['bleed'] ?? 3,
                $data['gutter'] ?? 5,
                $data['version_layout_mode'] ?? 'ganged'
            );
        } else {
            $imposition = $this->impositionService->calculateImposition(
                $data['item_width'],
                $data['item_height'],
                $data['sheet_width'],
                $data['sheet_height'],
                $quantity,
                $data['bleed'] ?? 3,
                $data['gutter'] ?? 5
            );
        }

        if ($imposition['items_per_sheet'] === 0) {
            throw new \Exception('Items do not fit on the specified sheet size.');
        }

        // Calculate costs
        $materialCost = $this->calculateMaterialCost(
            $material,
            $imposition['sheets_required'],
            $data['sheet_width'],
            $data['sheet_height']
        );

        $productionCost = $this->calculateProductionCost(
            $machine,
            $imposition['sheets_required']
        );

        $setupCost = $productionMethod->setup_cost;
        $totalCost = $materialCost + $productionCost + $setupCost;

        $markupPercentage = $data['markup_percentage'] ?? 0;
        $finalPrice = $totalCost * (1 + ($markupPercentage / 100));

        return [
            'imposition' => $imposition,
            'costs' => [
                'material_cost' => round($materialCost, 2),
                'production_cost' => round($productionCost, 2),
                'setup_cost' => round($setupCost, 2),
                'total_cost' => round($totalCost, 2),
                'markup_percentage' => $markupPercentage,
                'final_price' => round($finalPrice, 2),
            ],
            'breakdown' => [
                'cost_per_item' => round($finalPrice / $quantity, 4),
                'cost_per_sheet' => round($finalPrice / $imposition['sheets_required'], 2),
                'material_cost_per_sheet' => round($materialCost / $imposition['sheets_required'], 2),
                'production_time_hours' => $machine->calculateProductionTime($imposition['sheets_required']),
            ],
        ];
    }

    /**
     * Calculate material cost
     */
    protected function calculateMaterialCost(
        Material $material,
        int $sheets,
        int $sheetWidth,
        int $sheetHeight
    ): float {
        if ($material->is_roll) {
            // For roll materials, calculate based on area or linear meters
            if ($material->cost_per_linear_meter > 0) {
                $linearMeters = ($sheetHeight / 1000) * $sheets; // Convert mm to meters
                return $linearMeters * $material->cost_per_linear_meter;
            } elseif ($material->cost_per_sqm > 0) {
                $area = (($sheetWidth * $sheetHeight) / 1000000) * $sheets; // Convert mm² to m²
                return $area * $material->cost_per_sqm;
            }
        } else {
            // For sheet materials
            if ($material->cost_per_sheet > 0) {
                return $sheets * $material->cost_per_sheet;
            } elseif ($material->cost_per_sqm > 0) {
                $area = (($sheetWidth * $sheetHeight) / 1000000) * $sheets; // Convert mm² to m²
                return $area * $material->cost_per_sqm;
            }
        }

        return 0;
    }

    /**
     * Calculate production cost
     */
    protected function calculateProductionCost(Machine $machine, int $sheets): float
    {
        $baseCost = $machine->calculateTotalCost($sheets);

        // Add waste percentage
        $wasteMultiplier = 1 + ($machine->waste_percentage / 100);

        return $baseCost * $wasteMultiplier;
    }

    /**
     * Create and save an estimate
     */
    public function createEstimate(array $data): Estimate
    {
        $calculation = $this->calculateEstimate($data);

        // Determine if we have versions
        $hasVersions = isset($data['has_versions']) && $data['has_versions'] && !empty($data['versions']);
        $quantity = $hasVersions ? collect($data['versions'])->sum('quantity') : $data['quantity'];

        $estimate = Estimate::create([
            'customer_id' => $data['customer_id'] ?? null,
            'customer_contact_id' => $data['customer_contact_id'] ?? null,
            'client_name' => $data['client_name'],
            'job_title' => $data['job_title'],
            'description' => $data['description'] ?? null,
            'production_method_id' => $data['production_method_id'],
            'machine_id' => $data['machine_id'],
            'material_id' => $data['material_id'],
            'material_size_id' => $data['material_size_id'] ?? null,
            'quantity' => $hasVersions ? null : $data['quantity'],
            'has_versions' => $hasVersions,
            'versions' => $hasVersions ? $data['versions'] : null,
            'total_quantity' => $quantity,
            'item_width' => $data['item_width'],
            'item_height' => $data['item_height'],
            'sheet_width' => $data['sheet_width'],
            'sheet_height' => $data['sheet_height'],
            'items_per_sheet' => $calculation['imposition']['items_per_sheet'],
            'sheets_required' => $calculation['imposition']['sheets_required'],
            'material_cost' => $calculation['costs']['material_cost'],
            'production_cost' => $calculation['costs']['production_cost'],
            'setup_cost' => $calculation['costs']['setup_cost'],
            'total_cost' => $calculation['costs']['total_cost'],
            'markup_percentage' => $calculation['costs']['markup_percentage'],
            'final_price' => $calculation['costs']['final_price'],
            'imposition_layout' => $calculation['imposition'],
            'status' => 'draft',
        ]);

        return $estimate;
    }

    /**
     * Get cost comparison across different production methods
     */
    public function compareProductionMethods(array $data): array
    {
        $comparisons = [];
        $productionMethods = ProductionMethod::active()->with('activeMachines')->get();

        foreach ($productionMethods as $method) {
            foreach ($method->activeMachines as $machine) {
                try {
                    $testData = array_merge($data, [
                        'production_method_id' => $method->id,
                        'machine_id' => $machine->id,
                    ]);

                    $calculation = $this->calculateEstimate($testData);

                    $comparisons[] = [
                        'production_method' => $method->name,
                        'machine' => $machine->name,
                        'final_price' => $calculation['costs']['final_price'],
                        'production_time' => $calculation['breakdown']['production_time_hours'],
                        'efficiency' => $calculation['imposition']['efficiency_percentage'],
                        'items_per_sheet' => $calculation['imposition']['items_per_sheet'],
                        'sheets_required' => $calculation['imposition']['sheets_required'],
                    ];
                } catch (\Exception $e) {
                    // Skip if this combination doesn't work
                    continue;
                }
            }
        }

        // Sort by price (lowest first)
        usort($comparisons, function ($a, $b) {
            return $a['final_price'] <=> $b['final_price'];
        });

        return $comparisons;
    }

    /**
     * Get material recommendations based on production method
     */
    public function getRecommendedMaterials(int $productionMethodId): array
    {
        $materials = Material::active()->get();
        $recommendations = [];

        foreach ($materials as $material) {
            $suitability = $this->assessMaterialSuitability($material, $productionMethodId);
            
            if ($suitability['suitable']) {
                $recommendations[] = [
                    'material' => $material,
                    'suitability_score' => $suitability['score'],
                    'notes' => $suitability['notes'],
                ];
            }
        }

        // Sort by suitability score (highest first)
        usort($recommendations, function ($a, $b) {
            return $b['suitability_score'] <=> $a['suitability_score'];
        });

        return $recommendations;
    }

    /**
     * Assess material suitability for a production method
     */
    protected function assessMaterialSuitability(Material $material, int $productionMethodId): array
    {
        $method = ProductionMethod::find($productionMethodId);
        $score = 50; // Base score
        $notes = [];
        $suitable = true;

        if (!$method) {
            return ['suitable' => false, 'score' => 0, 'notes' => ['Invalid production method']];
        }

        // Digital print preferences
        if ($method->slug === 'digital') {
            if ($material->type === 'paper' && $material->weight <= 350) {
                $score += 30;
                $notes[] = 'Excellent for digital printing';
            } elseif ($material->type === 'vinyl' && !$material->is_roll) {
                $score += 20;
                $notes[] = 'Good for digital printing';
            } else {
                $score -= 20;
                $notes[] = 'May have limitations with digital printing';
            }
        }

        // Litho print preferences
        if ($method->slug === 'litho') {
            if ($material->type === 'paper' && !$material->is_roll) {
                $score += 40;
                $notes[] = 'Perfect for lithographic printing';
            } else {
                $score -= 30;
                $suitable = false;
                $notes[] = 'Not suitable for lithographic printing';
            }
        }

        // Wide format preferences
        if ($method->slug === 'wide-format') {
            if ($material->is_roll) {
                $score += 40;
                $notes[] = 'Ideal for wide format printing';
            } elseif ($material->type === 'vinyl' || $material->type === 'fabric') {
                $score += 20;
                $notes[] = 'Good for wide format applications';
            } else {
                $score -= 10;
                $notes[] = 'Limited suitability for wide format';
            }
        }

        return [
            'suitable' => $suitable && $score > 0,
            'score' => max(0, $score),
            'notes' => $notes,
        ];
    }
}
