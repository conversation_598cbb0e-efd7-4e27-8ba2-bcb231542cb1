<?php

namespace App\Services;

class ImpositionService
{
    /**
     * Calculate the optimal imposition for items on a sheet
     */
    public function calculateImposition(
        int $itemWidth,
        int $itemHeight,
        int $sheetWidth,
        int $sheetHeight,
        int $quantity,
        int $bleed = 3, // mm bleed around each item
        int $gutter = 5,  // mm space between items
        array $versions = [] // array of versions with quantities
    ): array {
        $results = [];
        
        // Try both orientations for the item
        $orientations = [
            ['width' => $itemWidth, 'height' => $itemHeight, 'rotated' => false],
            ['width' => $itemHeight, 'height' => $itemWidth, 'rotated' => true],
        ];
        
        foreach ($orientations as $orientation) {
            $result = $this->calculateLayout(
                $orientation['width'],
                $orientation['height'],
                $sheetWidth,
                $sheetHeight,
                $quantity,
                $bleed,
                $gutter,
                $orientation['rotated']
            );
            
            if ($result['items_per_sheet'] > 0) {
                $results[] = $result;
            }
        }
        
        // Return the best result (most items per sheet)
        if (empty($results)) {
            return $this->getEmptyResult();
        }
        
        usort($results, function ($a, $b) {
            return $b['items_per_sheet'] <=> $a['items_per_sheet'];
        });
        
        return $results[0];
    }
    
    /**
     * Calculate layout for a specific orientation
     */
    private function calculateLayout(
        int $itemWidth,
        int $itemHeight,
        int $sheetWidth,
        int $sheetHeight,
        int $quantity,
        int $bleed,
        int $gutter,
        bool $rotated
    ): array {
        // Add bleed to item dimensions
        $itemWithBleedWidth = $itemWidth + (2 * $bleed);
        $itemWithBleedHeight = $itemHeight + (2 * $bleed);
        
        // Calculate how many items fit horizontally and vertically
        $itemsHorizontal = $this->calculateItemsInDirection(
            $sheetWidth,
            $itemWithBleedWidth,
            $gutter
        );
        
        $itemsVertical = $this->calculateItemsInDirection(
            $sheetHeight,
            $itemWithBleedHeight,
            $gutter
        );
        
        $itemsPerSheet = $itemsHorizontal * $itemsVertical;
        $sheetsRequired = $itemsPerSheet > 0 ? ceil($quantity / $itemsPerSheet) : 0;
        
        // Calculate positions for visualization
        $positions = $this->calculatePositions(
            $itemsHorizontal,
            $itemsVertical,
            $itemWithBleedWidth,
            $itemWithBleedHeight,
            $gutter,
            min($quantity, $itemsPerSheet)
        );
        
        // Calculate efficiency
        $itemArea = $itemWidth * $itemHeight;
        $sheetArea = $sheetWidth * $sheetHeight;
        $usedArea = $itemsPerSheet * $itemArea;
        $efficiency = $sheetArea > 0 ? ($usedArea / $sheetArea) * 100 : 0;
        
        return [
            'items_per_sheet' => $itemsPerSheet,
            'sheets_required' => $sheetsRequired,
            'items_horizontal' => $itemsHorizontal,
            'items_vertical' => $itemsVertical,
            'efficiency_percentage' => round($efficiency, 2),
            'rotated' => $rotated,
            'positions' => $positions,
            'layout_data' => [
                'sheet_width' => $sheetWidth,
                'sheet_height' => $sheetHeight,
                'item_width' => $itemWidth,
                'item_height' => $itemHeight,
                'item_with_bleed_width' => $itemWithBleedWidth,
                'item_with_bleed_height' => $itemWithBleedHeight,
                'bleed' => $bleed,
                'gutter' => $gutter,
            ],
        ];
    }
    
    /**
     * Calculate how many items fit in one direction
     */
    private function calculateItemsInDirection(int $sheetDimension, int $itemDimension, int $gutter): int
    {
        if ($itemDimension > $sheetDimension) {
            return 0;
        }
        
        // First item doesn't need gutter before it
        $availableSpace = $sheetDimension - $itemDimension;
        $additionalItems = floor($availableSpace / ($itemDimension + $gutter));
        
        return 1 + $additionalItems;
    }
    
    /**
     * Calculate positions for each item on the sheet
     */
    private function calculatePositions(
        int $itemsHorizontal,
        int $itemsVertical,
        int $itemWidth,
        int $itemHeight,
        int $gutter,
        int $totalItems
    ): array {
        $positions = [];
        $itemCount = 0;
        
        for ($row = 0; $row < $itemsVertical && $itemCount < $totalItems; $row++) {
            for ($col = 0; $col < $itemsHorizontal && $itemCount < $totalItems; $col++) {
                $x = $col * ($itemWidth + $gutter);
                $y = $row * ($itemHeight + $gutter);
                
                $positions[] = [
                    'x' => $x,
                    'y' => $y,
                    'width' => $itemWidth,
                    'height' => $itemHeight,
                    'item_number' => $itemCount + 1,
                ];
                
                $itemCount++;
            }
        }
        
        return $positions;
    }
    
    /**
     * Get empty result when no items fit
     */
    private function getEmptyResult(): array
    {
        return [
            'items_per_sheet' => 0,
            'sheets_required' => 0,
            'items_horizontal' => 0,
            'items_vertical' => 0,
            'efficiency_percentage' => 0,
            'rotated' => false,
            'positions' => [],
            'layout_data' => [],
        ];
    }
    
    /**
     * Calculate waste percentage based on efficiency
     */
    public function calculateWaste(float $efficiency): float
    {
        return 100 - $efficiency;
    }
    
    /**
     * Validate if items can fit on the sheet at all
     */
    public function canItemFitOnSheet(
        int $itemWidth,
        int $itemHeight,
        int $sheetWidth,
        int $sheetHeight,
        int $bleed = 3
    ): bool {
        $itemWithBleedWidth = $itemWidth + (2 * $bleed);
        $itemWithBleedHeight = $itemHeight + (2 * $bleed);
        
        // Check both orientations
        return ($itemWithBleedWidth <= $sheetWidth && $itemWithBleedHeight <= $sheetHeight) ||
               ($itemWithBleedHeight <= $sheetWidth && $itemWithBleedWidth <= $sheetHeight);
    }
    
    /**
     * Get recommended sheet sizes for an item
     */
    public function getRecommendedSheetSizes(int $itemWidth, int $itemHeight): array
    {
        $standardSizes = [
            ['name' => 'A4', 'width' => 210, 'height' => 297],
            ['name' => 'A3', 'width' => 297, 'height' => 420],
            ['name' => 'A2', 'width' => 420, 'height' => 594],
            ['name' => 'A1', 'width' => 594, 'height' => 841],
            ['name' => 'SRA3', 'width' => 320, 'height' => 450],
            ['name' => 'SRA2', 'width' => 450, 'height' => 640],
            ['name' => 'SRA1', 'width' => 640, 'height' => 900],
            ['name' => 'B2', 'width' => 500, 'height' => 707],
            ['name' => 'B1', 'width' => 707, 'height' => 1000],
        ];
        
        $suitable = [];
        
        foreach ($standardSizes as $size) {
            if ($this->canItemFitOnSheet($itemWidth, $itemHeight, $size['width'], $size['height'])) {
                $imposition = $this->calculateImposition(
                    $itemWidth,
                    $itemHeight,
                    $size['width'],
                    $size['height'],
                    1
                );
                
                $suitable[] = array_merge($size, [
                    'items_per_sheet' => $imposition['items_per_sheet'],
                    'efficiency' => $imposition['efficiency_percentage'],
                ]);
            }
        }
        
        // Sort by efficiency (best first)
        usort($suitable, function ($a, $b) {
            return $b['efficiency'] <=> $a['efficiency'];
        });
        
        return $suitable;
    }

    /**
     * Calculate imposition with version support
     */
    public function calculateImpositionWithVersions(
        int $itemWidth,
        int $itemHeight,
        int $sheetWidth,
        int $sheetHeight,
        array $versions,
        int $bleed = 3,
        int $gutter = 5,
        string $layoutMode = 'ganged'
    ): array {
        $totalQuantity = collect($versions)->sum('quantity');

        if ($layoutMode === 'separate') {
            return $this->calculateSeparateSheetLayout($itemWidth, $itemHeight, $sheetWidth, $sheetHeight, $versions, $bleed, $gutter);
        }

        // Calculate ganged (mixed) imposition
        $baseResult = $this->calculateImposition(
            $itemWidth,
            $itemHeight,
            $sheetWidth,
            $sheetHeight,
            $totalQuantity,
            $bleed,
            $gutter
        );

        if ($baseResult['items_per_sheet'] === 0) {
            return $baseResult;
        }

        // Calculate version distribution across sheets
        $versionDistribution = $this->calculateVersionDistribution(
            $versions,
            $baseResult['items_per_sheet'],
            $baseResult['sheets_required']
        );

        // Update positions with version information
        $positionsWithVersions = $this->assignVersionsToPositions(
            $baseResult['positions'],
            $versionDistribution,
            $versions
        );

        return array_merge($baseResult, [
            'versions' => $versions,
            'total_quantity' => $totalQuantity,
            'version_distribution' => $versionDistribution,
            'positions_with_versions' => $positionsWithVersions,
            'layout_mode' => $layoutMode,
        ]);
    }

    /**
     * Calculate how versions are distributed across sheets
     */
    private function calculateVersionDistribution(array $versions, int $itemsPerSheet, int $sheetsRequired): array
    {
        $distribution = [];
        $totalItems = $itemsPerSheet * $sheetsRequired;

        foreach ($versions as $version) {
            $versionQuantity = $version['quantity'];
            $sheetsForVersion = ceil($versionQuantity / $itemsPerSheet);
            $itemsOnLastSheet = $versionQuantity % $itemsPerSheet;

            $distribution[] = [
                'name' => $version['name'],
                'quantity' => $versionQuantity,
                'sheets_needed' => $sheetsForVersion,
                'items_on_last_sheet' => $itemsOnLastSheet ?: $itemsPerSheet,
                'percentage' => ($versionQuantity / collect($versions)->sum('quantity')) * 100,
            ];
        }

        return $distribution;
    }

    /**
     * Assign versions to specific positions on sheets
     */
    private function assignVersionsToPositions(array $positions, array $versionDistribution, array $versions): array
    {
        $positionsWithVersions = [];

        // Create a flat array of all items with their version assignments
        $allItems = [];
        foreach ($versions as $version) {
            for ($i = 0; $i < $version['quantity']; $i++) {
                $allItems[] = [
                    'version_name' => $version['name'],
                    'version_item_number' => $i + 1,
                ];
            }
        }

        // For visualization, we only show one sheet but need to represent all versions
        // So we'll distribute versions proportionally across the positions shown
        $itemsPerSheet = count($positions);
        $totalQuantity = collect($versions)->sum('quantity');

        foreach ($positions as $index => $position) {
            // Calculate which version this position should represent based on proportional distribution
            $cumulativeQuantity = 0;
            $selectedVersion = $versions[0]; // fallback
            $versionItemNumber = 1;

            foreach ($versions as $version) {
                $versionStartIndex = $cumulativeQuantity;
                $versionEndIndex = $cumulativeQuantity + $version['quantity'] - 1;

                // Map the position index to the total item range
                $globalItemIndex = floor(($index / $itemsPerSheet) * $totalQuantity);

                if ($globalItemIndex >= $versionStartIndex && $globalItemIndex <= $versionEndIndex) {
                    $selectedVersion = $version;
                    $versionItemNumber = $globalItemIndex - $versionStartIndex + 1;
                    break;
                }

                $cumulativeQuantity += $version['quantity'];
            }

            $positionsWithVersions[] = array_merge($position, [
                'version_name' => $selectedVersion['name'],
                'version_item_number' => $versionItemNumber,
            ]);
        }

        return $positionsWithVersions;
    }

    /**
     * Calculate separate sheet layout for versions
     */
    private function calculateSeparateSheetLayout(
        int $itemWidth,
        int $itemHeight,
        int $sheetWidth,
        int $sheetHeight,
        array $versions,
        int $bleed,
        int $gutter
    ): array {
        $totalQuantity = collect($versions)->sum('quantity');
        $allPositions = [];
        $versionDistribution = [];
        $totalSheets = 0;
        $currentSheetOffset = 0;

        // Calculate layout for each version separately
        foreach ($versions as $index => $version) {
            $versionResult = $this->calculateImposition(
                $itemWidth,
                $itemHeight,
                $sheetWidth,
                $sheetHeight,
                $version['quantity'],
                $bleed,
                $gutter
            );

            if ($versionResult['items_per_sheet'] > 0) {
                // Add version-specific positions with sheet offset
                foreach ($versionResult['positions'] as $posIndex => $position) {
                    $allPositions[] = array_merge($position, [
                        'version_name' => $version['name'],
                        'version_item_number' => $posIndex + 1,
                        'sheet_number' => $currentSheetOffset + ceil(($posIndex + 1) / $versionResult['items_per_sheet']),
                    ]);
                }

                $versionDistribution[] = [
                    'name' => $version['name'],
                    'quantity' => $version['quantity'],
                    'sheets_needed' => $versionResult['sheets_required'],
                    'items_on_last_sheet' => $version['quantity'] % $versionResult['items_per_sheet'] ?: $versionResult['items_per_sheet'],
                    'percentage' => ($version['quantity'] / $totalQuantity) * 100,
                    'sheet_start' => $currentSheetOffset + 1,
                    'sheet_end' => $currentSheetOffset + $versionResult['sheets_required'],
                ];

                $currentSheetOffset += $versionResult['sheets_required'];
                $totalSheets += $versionResult['sheets_required'];
            }
        }

        // Use the first version's layout data as base
        $firstVersionResult = $this->calculateImposition($itemWidth, $itemHeight, $sheetWidth, $sheetHeight, $versions[0]['quantity'], $bleed, $gutter);

        return [
            'items_per_sheet' => $firstVersionResult['items_per_sheet'],
            'sheets_required' => $totalSheets,
            'items_horizontal' => $firstVersionResult['items_horizontal'],
            'items_vertical' => $firstVersionResult['items_vertical'],
            'efficiency_percentage' => $firstVersionResult['efficiency_percentage'],
            'rotated' => $firstVersionResult['rotated'],
            'positions' => $allPositions,
            'positions_with_versions' => $allPositions,
            'layout_data' => $firstVersionResult['layout_data'],
            'versions' => $versions,
            'total_quantity' => $totalQuantity,
            'version_distribution' => $versionDistribution,
            'layout_mode' => 'separate',
        ];
    }
}
