<?php

namespace App\Services;

use App\Models\Material;
use App\Models\MaterialSize;
use App\Models\Machine;
use App\Models\Size;
use Illuminate\Support\Collection;

class EstimationService
{
    /**
     * Calculate estimate options for given parameters
     */
    public function calculateEstimateOptions(array $params): Collection
    {
        $material = Material::find($params['material_id']);
        $finishedWidth = $params['finished_width'];
        $finishedHeight = $params['finished_height'];
        $quantity = $params['quantity'];
        $bleed = $params['bleed'] ?? 3; // Default 3mm bleed
        $gutter = $params['gutter'] ?? 3; // Default 3mm gutter
        
        if (!$material) {
            return collect();
        }

        // Get all available sizes for this material
        $materialSizes = MaterialSize::where('material_id', $material->id)
            ->where('is_available', true)
            ->with(['size', 'material'])
            ->get();

        $options = collect();

        foreach ($materialSizes as $materialSize) {
            // Calculate imposition for this size
            $impositionResult = $this->calculateImposition(
                $finishedWidth,
                $finishedHeight,
                $materialSize->getWidthMmAttribute(),
                $materialSize->getHeightMmAttribute(),
                $bleed,
                $gutter
            );

            if ($impositionResult['items_per_sheet'] > 0) {
                // Find compatible machines
                $compatibleMachines = $this->getCompatibleMachines(
                    $materialSize->getWidthMmAttribute(),
                    $materialSize->getHeightMmAttribute(),
                    $material->isSheet()
                );

                foreach ($compatibleMachines as $machine) {
                    $option = $this->calculateOption(
                        $material,
                        $materialSize,
                        $machine,
                        $impositionResult,
                        $quantity
                    );

                    $options->push($option);
                }
            }
        }

        // Sort by gross margin (highest first)
        return $options->sortByDesc('gross_margin');
    }

    /**
     * Calculate imposition layout
     */
    protected function calculateImposition(
        int $finishedWidth,
        int $finishedHeight,
        int $sheetWidth,
        int $sheetHeight,
        int $bleed = 3,
        int $gutter = 3
    ): array {
        // Add bleed to finished size
        $itemWidth = $finishedWidth + (2 * $bleed);
        $itemHeight = $finishedHeight + (2 * $bleed);

        // Calculate how many items fit
        $itemsAcross = floor(($sheetWidth + $gutter) / ($itemWidth + $gutter));
        $itemsDown = floor(($sheetHeight + $gutter) / ($itemHeight + $gutter));
        $itemsPerSheet = $itemsAcross * $itemsDown;

        // Try rotation if it gives better imposition
        $itemsAcrossRotated = floor(($sheetWidth + $gutter) / ($itemHeight + $gutter));
        $itemsDownRotated = floor(($sheetHeight + $gutter) / ($itemWidth + $gutter));
        $itemsPerSheetRotated = $itemsAcrossRotated * $itemsDownRotated;

        if ($itemsPerSheetRotated > $itemsPerSheet) {
            return [
                'items_per_sheet' => $itemsPerSheetRotated,
                'items_across' => $itemsAcrossRotated,
                'items_down' => $itemsDownRotated,
                'rotated' => true,
                'waste_percentage' => $this->calculateWastePercentage(
                    $sheetWidth * $sheetHeight,
                    $itemsPerSheetRotated * $itemHeight * $itemWidth
                )
            ];
        }

        return [
            'items_per_sheet' => $itemsPerSheet,
            'items_across' => $itemsAcross,
            'items_down' => $itemsDown,
            'rotated' => false,
            'waste_percentage' => $this->calculateWastePercentage(
                $sheetWidth * $sheetHeight,
                $itemsPerSheet * $itemWidth * $itemHeight
            )
        ];
    }

    /**
     * Get machines compatible with sheet size and material type
     */
    protected function getCompatibleMachines(int $sheetWidth, int $sheetHeight, bool $isSheet): Collection
    {
        return Machine::active()
            ->where(function ($query) use ($isSheet) {
                if ($isSheet) {
                    $query->where('type', 'sheet-fed');
                } else {
                    $query->where('type', 'roll-fed');
                }
            })
            ->get()
            ->filter(function ($machine) use ($sheetWidth, $sheetHeight) {
                return $machine->canHandleSheetSize($sheetWidth, $sheetHeight);
            });
    }

    /**
     * Calculate a single estimate option
     */
    protected function calculateOption(
        Material $material,
        MaterialSize $materialSize,
        Machine $machine,
        array $impositionResult,
        int $quantity
    ): array {
        $itemsPerSheet = $impositionResult['items_per_sheet'];
        $sheetsNeeded = ceil($quantity / $itemsPerSheet);
        
        // Add waste percentage
        $wasteSheets = ceil($sheetsNeeded * ($machine->waste_percentage / 100));
        $totalSheets = $sheetsNeeded + $wasteSheets;

        // Calculate costs
        $materialCost = $this->calculateMaterialCost($material, $materialSize, $totalSheets);
        $setupCost = $machine->setup_cost;
        $runCost = $machine->calculateRunCost($totalSheets);
        $totalCost = $materialCost + $setupCost + $runCost;

        // Calculate sell price with margin
        $sellPrice = $this->calculateSellPrice($totalCost, $materialCost);
        $grossMargin = $sellPrice - $materialCost;

        return [
            'material_id' => $material->id,
            'material_name' => $material->name,
            'material_size_id' => $materialSize->id,
            'size_name' => $materialSize->size?->name ?? 'Custom',
            'sheet_dimensions' => $materialSize->dimensions,
            'machine_id' => $machine->id,
            'machine_name' => $machine->name,
            'machine_type' => $machine->type,
            'imposition' => $impositionResult,
            'sheets_needed' => $sheetsNeeded,
            'waste_sheets' => $wasteSheets,
            'total_sheets' => $totalSheets,
            'material_cost' => $materialCost,
            'setup_cost' => $setupCost,
            'run_cost' => $runCost,
            'total_cost' => $totalCost,
            'sell_price' => $sellPrice,
            'gross_margin' => $grossMargin,
            'margin_percentage' => $materialCost > 0 ? ($grossMargin / $materialCost) * 100 : 0,
            'cost_per_1000' => $materialSize->calculateCostPer1000Sheets(),
            'pack_types' => $this->getPackTypeOptions($materialSize, $totalSheets),
        ];
    }

    /**
     * Calculate material cost using £/ton to £/1000 sheets conversion
     */
    protected function calculateMaterialCost(Material $material, MaterialSize $materialSize, int $sheets): float
    {
        if ($material->isRoll()) {
            // For rolls, use different calculation
            return $materialSize->calculateCostPerSqm() * $materialSize->area_sqm * $sheets;
        }

        // For sheets, use cost per 1000 sheets
        $costPer1000 = $materialSize->calculateCostPer1000Sheets();
        return ($sheets / 1000) * $costPer1000;
    }

    /**
     * Calculate sell price with maximum margin logic
     */
    protected function calculateSellPrice(float $totalCost, float $materialCost): float
    {
        // Exclude material cost from margin calculation
        $labourCost = $totalCost - $materialCost;
        
        // Apply margin to labour cost only (typical print industry practice)
        $labourMargin = $labourCost * 2.5; // 150% margin on labour
        
        return $materialCost + $labourMargin;
    }

    /**
     * Get pack type options for the material size
     */
    protected function getPackTypeOptions(MaterialSize $materialSize, int $totalSheets): array
    {
        $options = [];

        if ($materialSize->ream_quantity && $materialSize->ream_quantity > 0) {
            $reams = ceil($totalSheets / $materialSize->ream_quantity);
            $options['ream'] = [
                'type' => 'Ream Wrap',
                'quantity_per_pack' => $materialSize->ream_quantity,
                'packs_needed' => $reams,
                'total_sheets' => $reams * $materialSize->ream_quantity,
            ];
        }

        if ($materialSize->bulk_quantity && $materialSize->bulk_quantity > 0) {
            $bulks = ceil($totalSheets / $materialSize->bulk_quantity);
            $options['bulk'] = [
                'type' => 'Bulk Pallet',
                'quantity_per_pack' => $materialSize->bulk_quantity,
                'packs_needed' => $bulks,
                'total_sheets' => $bulks * $materialSize->bulk_quantity,
            ];
        }

        return $options;
    }

    /**
     * Calculate waste percentage
     */
    protected function calculateWastePercentage(float $totalArea, float $usedArea): float
    {
        if ($totalArea <= 0) {
            return 0;
        }

        return ((($totalArea - $usedArea) / $totalArea) * 100);
    }

    /**
     * Get the recommended option (highest gross margin)
     */
    public function getRecommendedOption(Collection $options): ?array
    {
        return $options->sortByDesc('gross_margin')->first();
    }
}
