<?php

namespace App\Services;

use App\Models\InkandToner;

class InkCostEstimatorService
{
    /**
     * Estimate ink cost per sheet including multi-pass logic.
     *
     * @param InkAndToner $ink
     * @param float $widthMm Width in mm
     * @param float $heightMm Height in mm
     * @param float $frontCoveragePercent 0–100 coverage on front
     * @param float|null $backCoveragePercent 0–100 coverage on back
     * @param bool $canDuplex True if press can print both sides in one pass
     * @return float
     */
    public function calculateCostPerSheet(
        InkAndToner $ink,
        float $widthMm,
        float $heightMm,
        float $frontCoveragePercent,
        ?float $backCoveragePercent = null,
        bool $canDuplex = false
    ): float {
        $areaM2 = ($widthMm / 1000) * ($heightMm / 1000);

        $frontCoverage = ($frontCoveragePercent / 100);
        $backCoverage = ($backCoveragePercent ?? 0) / 100;

        $passes = $backCoverage > 0
            ? ($canDuplex ? 1 : 2)
            : 1;

        // Total coverage across all passes
        $totalCoverage = $frontCoverage + $backCoverage;

        // Final cost calculation
        return $areaM2 * $totalCoverage * $ink->consumption_rate_kg_per_m2 * $ink->cost_per_kg;
    }
}