<?php

namespace App\Services;

use App\Models\Machine;
use App\Models\Material;
use App\Models\Size;
use App\Models\ProductionMethod;

class ProductionOptionService
{
    public function __construct(
        private EstimateService $estimateService,
        private ImpositionService $impositionService
    ) {}

    /**
     * Calculate all possible production options for given specifications
     */
    public function calculateAllProductionOptions(array $data): array
    {
        $options = [];
        $material = Material::find($data['material_id']);
        
        if (!$material) {
            return [];
        }

        // Get all active machines
        $machines = Machine::where('is_active', true)->with('productionMethod')->get();
        
        // Get all available sizes for the material
        $availableSizes = $material->sizes()
            ->wherePivot('is_available', true)
            ->get();

        foreach ($machines as $machine) {
            foreach ($availableSizes as $size) {
                try {
                    // Check if item fits on this size
                    if (!$this->itemFitsOnSize($data, $size)) {
                        continue;
                    }

                    // Check if machine can handle this material size
                    if (!$this->machineCanHandleSize($machine, $size)) {
                        continue;
                    }

                    $optionData = array_merge($data, [
                        'production_method_id' => $machine->production_method_id,
                        'machine_id' => $machine->id,
                        'size_id' => $size->id,
                        'sheet_width' => $size->width_mm,
                        'sheet_height' => $size->height_mm,
                    ]);

                    $calculation = $this->estimateService->calculateEstimate($optionData);
                    
                    $options[] = [
                        'id' => $machine->id . '_' . ($size->id ?? 'custom'),
                        'machine' => $machine,
                        'production_method' => $machine->productionMethod,
                        'material' => $material,
                        'material_size' => $size,
                        'calculation' => $calculation,
                        'efficiency' => $calculation['imposition']['efficiency_percentage'],
                        'final_price' => $calculation['costs']['final_price'],
                        'cost_per_item' => $calculation['breakdown']['cost_per_item'],
                        'sheets_required' => $calculation['imposition']['sheets_required'],
                        'items_per_sheet' => $calculation['imposition']['items_per_sheet'],
                        'production_time' => $calculation['breakdown']['production_time_hours'],
                    ];
                } catch (\Exception $e) {
                    // Skip this combination if calculation fails
                    continue;
                }
            }
        }

        // Sort by efficiency (highest first), then by price (lowest first)
        usort($options, function ($a, $b) {
            $efficiencyDiff = $b['efficiency'] - $a['efficiency'];
            if (abs($efficiencyDiff) < 0.1) { // If efficiency is very close
                return $a['final_price'] <=> $b['final_price']; // Sort by price
            }
            return $efficiencyDiff <=> 0;
        });

        return $options;
    }

    /**
     * Check if item dimensions fit on the given material size
     */
    private function itemFitsOnSize(array $data, $size): bool
    {
        $itemWidth = $data['item_width'] + (($data['bleed'] ?? 3) * 2);
        $itemHeight = $data['item_height'] + (($data['bleed'] ?? 3) * 2);
        
        // Check if item fits normally
        if ($itemWidth <= $size->width_mm && $itemHeight <= $size->height_mm) {
            return true;
        }
        
        // Check if item fits when rotated
        if ($itemHeight <= $size->width_mm && $itemWidth <= $size->height_mm) {
            return true;
        }
        
        return false;
    }

    /**
     * Check if machine can handle the given material size
     */
    private function machineCanHandleSize($machine, $size): bool
    {
        // Check if machine has size constraints
        if ($machine->max_sheet_width && $size->width_mm > $machine->max_sheet_width) {
            return false;
        }

        if ($machine->max_sheet_height && $size->height_mm > $machine->max_sheet_height) {
            return false;
        }

        if ($machine->min_sheet_width && $size->width_mm < $machine->min_sheet_width) {
            return false;
        }

        if ($machine->min_sheet_height && $size->height_mm < $machine->min_sheet_height) {
            return false;
        }

        return true;
    }

    /**
     * Get the best production option (highest efficiency, lowest cost)
     */
    public function getBestProductionOption(array $data): ?array
    {
        $options = $this->calculateAllProductionOptions($data);
        return $options[0] ?? null;
    }

    /**
     * Filter production options by criteria
     */
    public function filterProductionOptions(array $options, array $filters = []): array
    {
        $filtered = $options;

        if (isset($filters['max_price'])) {
            $filtered = array_filter($filtered, fn($option) => $option['final_price'] <= $filters['max_price']);
        }

        if (isset($filters['min_efficiency'])) {
            $filtered = array_filter($filtered, fn($option) => $option['efficiency'] >= $filters['min_efficiency']);
        }

        if (isset($filters['production_method_id'])) {
            $filtered = array_filter($filtered, fn($option) => $option['production_method']->id == $filters['production_method_id']);
        }

        if (isset($filters['machine_id'])) {
            $filtered = array_filter($filtered, fn($option) => $option['machine']->id == $filters['machine_id']);
        }

        return array_values($filtered);
    }

    /**
     * Group production options by production method
     */
    public function groupOptionsByProductionMethod(array $options): array
    {
        $grouped = [];
        
        foreach ($options as $option) {
            $methodName = $option['production_method']->name;
            if (!isset($grouped[$methodName])) {
                $grouped[$methodName] = [];
            }
            $grouped[$methodName][] = $option;
        }
        
        return $grouped;
    }

    /**
     * Get production option statistics
     */
    public function getProductionOptionStats(array $options): array
    {
        if (empty($options)) {
            return [];
        }

        $prices = array_column($options, 'final_price');
        $efficiencies = array_column($options, 'efficiency');
        $productionTimes = array_column($options, 'production_time');

        return [
            'total_options' => count($options),
            'price_range' => [
                'min' => min($prices),
                'max' => max($prices),
                'average' => array_sum($prices) / count($prices),
            ],
            'efficiency_range' => [
                'min' => min($efficiencies),
                'max' => max($efficiencies),
                'average' => array_sum($efficiencies) / count($efficiencies),
            ],
            'production_time_range' => [
                'min' => min($productionTimes),
                'max' => max($productionTimes),
                'average' => array_sum($productionTimes) / count($productionTimes),
            ],
            'best_efficiency' => max($efficiencies),
            'lowest_price' => min($prices),
            'fastest_production' => min($productionTimes),
        ];
    }
}
