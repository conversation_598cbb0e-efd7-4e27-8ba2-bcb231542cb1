<?php

use Illuminate\Support\Facades\Route;
use Livewire\Volt\Volt;

Route::get('/', function () {
    return view('welcome');
})->name('home');

Route::view('dashboard', 'dashboard')
    ->middleware(['auth', 'verified'])
    ->name('dashboard');

Route::middleware(['auth'])->group(function () {
    Route::redirect('settings', 'settings/profile');

    Volt::route('settings/profile', 'settings.profile')->name('settings.profile');
    Volt::route('settings/password', 'settings.password')->name('settings.password');
    Volt::route('settings/appearance', 'settings.appearance')->name('settings.appearance');
});

// Public estimate routes (no authentication required)
Route::prefix('estimates')->name('estimates.')->group(function () {
    Route::get('/{hash}', [App\Http\Controllers\PublicEstimateController::class, 'show'])
        ->name('public.show');
    Route::post('/{hash}/accept', [App\Http\Controllers\PublicEstimateController::class, 'accept'])
        ->name('public.accept');
    Route::post('/{hash}/decline', [App\Http\Controllers\PublicEstimateController::class, 'decline'])
        ->name('public.decline');
    Route::get('/{estimate}/email', [App\Http\Controllers\PublicEstimateController::class, 'email'])
        ->name('email')
        ->middleware('auth');
});

require __DIR__.'/auth.php';
