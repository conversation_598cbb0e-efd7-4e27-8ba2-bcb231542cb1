<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('estimates', function (Blueprint $table) {
            $table->id();
            $table->string('estimate_number')->unique();
            $table->string('client_name');
            $table->string('job_title');
            $table->text('description')->nullable();
            $table->foreignId('production_method_id')->constrained()->onDelete('cascade');
            $table->foreignId('machine_id')->constrained()->onDelete('cascade');
            $table->foreignId('material_id')->constrained()->onDelete('cascade');
            $table->integer('quantity'); // Number of finished items needed
            $table->integer('item_width'); // Width of individual item in mm
            $table->integer('item_height'); // Height of individual item in mm
            $table->integer('sheet_width'); // Sheet/roll width in mm
            $table->integer('sheet_height'); // Sheet/roll height in mm
            $table->integer('items_per_sheet'); // Calculated items that fit per sheet
            $table->integer('sheets_required'); // Total sheets needed
            $table->decimal('material_cost', 10, 2); // Total material cost
            $table->decimal('production_cost', 10, 2); // Total production cost
            $table->decimal('setup_cost', 10, 2); // Setup cost
            $table->decimal('total_cost', 10, 2); // Total estimate cost
            $table->decimal('markup_percentage', 5, 2)->default(0); // Markup %
            $table->decimal('final_price', 10, 2); // Final price with markup
            $table->json('imposition_layout')->nullable(); // Store layout data for visualization
            $table->enum('status', ['draft', 'sent', 'approved', 'rejected'])->default('draft');
            $table->timestamp('sent_at')->nullable();
            $table->timestamp('approved_at')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('estimates');
    }
};
