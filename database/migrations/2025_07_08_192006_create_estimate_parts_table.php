<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('estimate_parts', function (Blueprint $table) {
            $table->id();
            $table->foreignId('estimate_id')->constrained()->onDelete('cascade');
            $table->foreignId('product_id')->constrained()->onDelete('restrict');

            // Part identification
            $table->string('part_name');
            $table->text('description')->nullable();
            $table->integer('sort_order')->default(0);

            // Production setup
            $table->foreignId('production_method_id')->constrained()->onDelete('restrict');
            $table->foreignId('machine_id')->constrained()->onDelete('restrict');
            $table->foreignId('material_id')->constrained()->onDelete('restrict');
            $table->foreignId('material_size_id')->nullable()->constrained()->onDelete('set null');

            // Quantities and specifications
            $table->integer('quantity')->nullable();
            $table->boolean('has_versions')->default(false);
            $table->json('versions')->nullable();
            $table->integer('total_quantity');
            $table->integer('item_width');
            $table->integer('item_height');
            $table->integer('sheet_width');
            $table->integer('sheet_height');

            // Production results
            $table->integer('items_per_sheet');
            $table->integer('sheets_required');
            $table->json('imposition_layout')->nullable();
            $table->boolean('optimized')->default(true);

            // Costs
            $table->decimal('material_cost', 10, 2);
            $table->decimal('production_cost', 10, 2);
            $table->decimal('setup_cost', 10, 2);
            $table->decimal('total_cost', 10, 2);
            $table->decimal('markup_percentage', 5, 2);
            $table->decimal('final_price', 10, 2);

            // Product-specific fields
            $table->integer('pages')->nullable(); // For bound products
            $table->string('binding_type')->nullable(); // saddle, perfect, etc.
            $table->string('fold_type')->nullable(); // half, tri, z-fold, etc.
            $table->json('finishing_options')->nullable(); // lamination, UV, etc.

            $table->timestamps();

            $table->index(['estimate_id', 'sort_order']);
            $table->index(['product_id']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('estimate_parts');
    }
};
