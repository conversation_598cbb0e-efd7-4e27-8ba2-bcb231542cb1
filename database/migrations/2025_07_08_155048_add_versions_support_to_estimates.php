<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('estimates', function (Blueprint $table) {
            // Add support for multiple versions
            $table->boolean('has_versions')->default(false)->after('quantity');
            $table->json('versions')->nullable()->after('has_versions');
            $table->integer('total_quantity')->nullable()->after('versions');
        });

        Schema::table('estimate_items', function (Blueprint $table) {
            // Add version support to items
            $table->string('version_name')->nullable()->after('name');
            $table->integer('version_quantity')->nullable()->after('quantity');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('estimates', function (Blueprint $table) {
            $table->dropColumn(['has_versions', 'versions', 'total_quantity']);
        });

        Schema::table('estimate_items', function (Blueprint $table) {
            $table->dropColumn(['version_name', 'version_quantity']);
        });
    }
};
