<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('production_methods', function (Blueprint $table) {
            $table->id();
            $table->string('name'); // e.g., 'Digital Print', 'Litho Print', 'Wide Format'
            $table->string('slug')->unique(); // e.g., 'digital', 'litho', 'wide-format'
            $table->text('description')->nullable();
            $table->decimal('setup_cost', 10, 2)->default(0); // Fixed setup cost
            $table->boolean('is_active')->default(true);
            $table->json('capabilities')->nullable(); // Store capabilities like max sheet size, etc.
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('production_methods');
    }
};
