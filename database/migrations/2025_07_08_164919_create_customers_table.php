<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('customers', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->string('company_number')->nullable();
            $table->string('vat_number')->nullable();
            $table->text('description')->nullable();
            $table->string('website')->nullable();
            $table->enum('status', ['active', 'inactive', 'prospect'])->default('active');
            $table->decimal('credit_limit', 10, 2)->nullable();
            $table->enum('payment_terms', ['immediate', '7_days', '14_days', '30_days', '60_days', '90_days'])->default('30_days');
            $table->text('notes')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('customers');
    }
};
