<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('estimate_items', function (Blueprint $table) {
            $table->id();
            $table->foreignId('estimate_id')->constrained()->onDelete('cascade');
            $table->string('name'); // Item name/description
            $table->integer('width'); // Item width in mm
            $table->integer('height'); // Item height in mm
            $table->integer('quantity'); // Quantity of this specific item
            $table->decimal('x_position', 8, 2)->nullable(); // X position in imposition
            $table->decimal('y_position', 8, 2)->nullable(); // Y position in imposition
            $table->boolean('rotated')->default(false); // Is this item rotated in the layout?
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('estimate_items');
    }
};
