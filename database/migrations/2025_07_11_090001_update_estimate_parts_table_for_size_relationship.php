<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('estimate_parts', function (Blueprint $table) {
            // Add size_id column
            $table->foreignId('size_id')->nullable()->after('material_id')->constrained('sizes')->onDelete('set null');
            
            // Drop the old material_size_id column
            $table->dropForeign(['material_size_id']);
            $table->dropColumn('material_size_id');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('estimate_parts', function (Blueprint $table) {
            // Restore material_size_id column
            $table->foreignId('material_size_id')->nullable()->after('material_id')->constrained('material_sizes')->onDelete('set null');
            
            // Drop size_id column
            $table->dropForeign(['size_id']);
            $table->dropColumn('size_id');
        });
    }
};
