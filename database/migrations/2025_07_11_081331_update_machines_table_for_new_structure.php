<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('machines', function (Blueprint $table) {
            // Add new machine type field
            $table->enum('type', ['sheet-fed', 'roll-fed'])->default('sheet-fed')->after('name');

            // Add setup cost
            $table->decimal('setup_cost', 10, 2)->default(0)->after('hourly_rate');

            // Rename and update speed field to be more generic
            $table->renameColumn('sheets_per_hour', 'run_speed');

            // Add speed unit to make it extensible
            $table->enum('speed_unit', ['sheets/hour', 'metres/hour'])->default('sheets/hour')->after('run_speed');

            // Rename size constraint fields for clarity
            $table->renameColumn('max_sheet_width', 'max_size_width');
            $table->renameColumn('max_sheet_height', 'max_size_height');
            $table->renameColumn('min_sheet_width', 'min_size_width');
            $table->renameColumn('min_sheet_height', 'min_size_height');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('machines', function (Blueprint $table) {
            $table->dropColumn(['type', 'setup_cost', 'speed_unit']);
            $table->renameColumn('run_speed', 'sheets_per_hour');
            $table->renameColumn('max_size_width', 'max_sheet_width');
            $table->renameColumn('max_size_height', 'max_sheet_height');
            $table->renameColumn('min_size_width', 'min_sheet_width');
            $table->renameColumn('min_size_height', 'min_sheet_height');
        });
    }
};
