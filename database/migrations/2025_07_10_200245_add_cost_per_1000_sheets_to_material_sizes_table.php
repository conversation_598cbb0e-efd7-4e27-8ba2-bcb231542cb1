<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('material_sizes', function (Blueprint $table) {
            $table->decimal('cost_per_1000_sheets', 10, 2)->nullable()->after('cost_per_sheet');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('material_sizes', function (Blueprint $table) {
            $table->dropColumn('cost_per_1000_sheets');
        });
    }
};
