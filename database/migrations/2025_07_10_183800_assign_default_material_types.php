<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Get material types
        $sheetType = \App\Models\MaterialType::where('slug', 'sheet')->first();
        $rollType = \App\Models\MaterialType::where('slug', 'roll')->first();

        if ($sheetType && $rollType) {
            // Assign sheet type to non-roll materials
            \App\Models\Material::where('is_roll', false)
                ->whereNull('material_type_id')
                ->update(['material_type_id' => $sheetType->id]);

            // Assign roll type to roll materials
            \App\Models\Material::where('is_roll', true)
                ->whereNull('material_type_id')
                ->update(['material_type_id' => $rollType->id]);
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Set all material_type_id to null
        \App\Models\Material::whereNotNull('material_type_id')
            ->update(['material_type_id' => null]);
    }
};
