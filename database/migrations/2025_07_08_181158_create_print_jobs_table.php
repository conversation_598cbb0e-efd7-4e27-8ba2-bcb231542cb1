<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('print_jobs', function (Blueprint $table) {
            $table->id();
            $table->string('job_number')->unique();
            $table->foreignId('estimate_id')->constrained()->onDelete('cascade');
            $table->foreignId('customer_id')->nullable()->constrained()->onDelete('set null');

            // Job details
            $table->string('client_name');
            $table->string('job_title');
            $table->text('description')->nullable();
            $table->text('notes')->nullable();

            // Status and dates
            $table->enum('status', ['pending', 'in_progress', 'completed', 'cancelled', 'on_hold'])->default('pending');
            $table->date('due_date')->nullable();
            $table->date('started_at')->nullable();
            $table->date('completed_at')->nullable();

            // Production details (copied from estimate)
            $table->foreignId('production_method_id')->constrained()->onDelete('restrict');
            $table->foreignId('machine_id')->constrained()->onDelete('restrict');
            $table->foreignId('material_id')->constrained()->onDelete('restrict');
            $table->foreignId('material_size_id')->nullable()->constrained()->onDelete('set null');

            // Quantities and specifications
            $table->integer('quantity')->nullable();
            $table->boolean('has_versions')->default(false);
            $table->json('versions')->nullable();
            $table->integer('total_quantity');
            $table->integer('item_width');
            $table->integer('item_height');
            $table->integer('sheet_width');
            $table->integer('sheet_height');

            // Production results
            $table->integer('items_per_sheet');
            $table->integer('sheets_required');
            $table->json('imposition_layout')->nullable();

            // Costs (locked from estimate)
            $table->decimal('material_cost', 10, 2);
            $table->decimal('production_cost', 10, 2);
            $table->decimal('setup_cost', 10, 2);
            $table->decimal('total_cost', 10, 2);
            $table->decimal('markup_percentage', 5, 2);
            $table->decimal('final_price', 10, 2);

            // Actual costs (for tracking)
            $table->decimal('actual_material_cost', 10, 2)->nullable();
            $table->decimal('actual_production_cost', 10, 2)->nullable();
            $table->decimal('actual_total_cost', 10, 2)->nullable();

            $table->timestamps();

            $table->index(['status', 'due_date']);
            $table->index(['customer_id', 'status']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('print_jobs');
    }
};
