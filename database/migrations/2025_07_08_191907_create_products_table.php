<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('products', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->string('slug')->unique();
            $table->text('description')->nullable();
            $table->enum('category', ['flat', 'folded', 'bound', 'specialty'])->default('flat');
            $table->json('specifications')->nullable(); // Store product-specific specs
            $table->json('production_methods')->nullable(); // Compatible production methods
            $table->boolean('requires_binding')->default(false);
            $table->boolean('requires_folding')->default(false);
            $table->boolean('requires_cutting')->default(true);
            $table->integer('min_pages')->nullable();
            $table->integer('max_pages')->nullable();
            $table->decimal('setup_cost_multiplier', 5, 2)->default(1.00);
            $table->boolean('is_active')->default(true);
            $table->integer('sort_order')->default(0);
            $table->timestamps();

            $table->index(['category', 'is_active']);
            $table->index('sort_order');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('products');
    }
};
