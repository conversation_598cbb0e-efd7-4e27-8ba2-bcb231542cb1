<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('machines', function (Blueprint $table) {
            $table->integer('max_weight')->nullable()->after('max_size_height');
            $table->integer('min_weight')->nullable()->after('max_weight');
            $table->boolean('can_duplex')->default(false)->after('min_weight');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('machines', function (Blueprint $table) {
            $table->dropColumn(['max_weight', 'min_weight', 'can_duplex']);
        });
    }
};
