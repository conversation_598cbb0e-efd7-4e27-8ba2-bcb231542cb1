<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('materials', function (Blueprint $table) {
            // Add sheet costing fields
            $table->decimal('cost_per_ton', 10, 2)->nullable()->after('caliper');

            // Add roll costing fields
            $table->decimal('roll_cost', 10, 2)->nullable()->after('roll_width');
            $table->decimal('roll_length_meters', 10, 2)->nullable()->after('roll_cost');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('materials', function (Blueprint $table) {
            $table->dropColumn([
                'cost_per_ton',
                'roll_cost',
                'roll_length_meters'
            ]);
        });
    }
};
