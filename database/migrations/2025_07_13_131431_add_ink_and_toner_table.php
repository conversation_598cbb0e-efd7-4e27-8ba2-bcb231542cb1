<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('ink_and_toner', function (Blueprint $table) {
            $table->id();
            $table->string('name');                      // E.g., "Cyan", "Black", "Pantone 123"
            $table->string('manufacturer')->nullable(); // E.g., "HP", "Canon", "Pantone"
            $table->enum('type', ['ink', 'toner']);      // Limits values to "ink" or "toner"
            $table->enum('colour_type', ['cmyk', 'pantone', 'metallic', 'neon', 'other'])->default('cmyk');  // True if part of CMYK set
            $table->decimal('cost_per_kg', 8, 2);        // Currency format, e.g., £12.50
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('ink_and_toner');
    }
};
