<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('material_sizes', function (Blueprint $table) {
            // Add size relationship
            $table->foreignId('size_id')->nullable()->after('material_id')->constrained()->onDelete('cascade');

            // Add pack type fields for sheet materials
            $table->integer('ream_quantity')->nullable()->after('cost_per_1000_sheets');
            $table->integer('bulk_quantity')->nullable()->after('ream_quantity');

            // Remove individual dimension fields since they'll come from Size
            // We'll keep them for now to avoid breaking existing data, but mark as nullable
            $table->string('name')->nullable()->change();
            $table->integer('width_mm')->nullable()->change();
            $table->integer('height_mm')->nullable()->change();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('material_sizes', function (Blueprint $table) {
            $table->dropForeign(['size_id']);
            $table->dropColumn(['size_id', 'ream_quantity', 'bulk_quantity']);

            // Restore required constraints
            $table->string('name')->nullable(false)->change();
            $table->integer('width_mm')->nullable(false)->change();
            $table->integer('height_mm')->nullable(false)->change();
        });
    }
};
