<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    public function up(): void
    {
        Schema::dropIfExists('estimates');

        Schema::create('estimates', function (Blueprint $table) {
            $table->id();
            $table->string('estimate_number')->unique();
            $table->string('status')->default('draft');
            $table->timestamp('sent_at')->nullable();
            $table->timestamp('approved_at')->nullable();
            $table->foreignId('customer_id')->nullable()->constrained()->nullOnDelete();
            $table->foreignId('customer_contact_id')->nullable()->constrained()->nullOnDelete();
            $table->string('public_hash')->nullable();
            $table->text('rejection_reason')->nullable();
            $table->timestamps();
        });

        Schema::create('estimate_parts', function (Blueprint $table) {
            $table->id();
            $table->foreignId('estimate_id')->constrained()->cascadeOnDelete();
            $table->string('job_title');
            $table->text('description')->nullable();
            $table->foreignId('product_id')->nullable()->constrained()->nullOnDelete();
            $table->foreignId('production_method_id')->constrained()->cascadeOnDelete();
            $table->foreignId('machine_id')->constrained()->cascadeOnDelete();
            $table->foreignId('material_id')->constrained()->cascadeOnDelete();
            $table->foreignId('size_id')->nullable()->constrained()->nullOnDelete();

            $table->integer('quantity')->nullable();
            $table->boolean('has_versions')->default(false);
            $table->integer('total_quantity')->nullable();

            $table->integer('item_width');
            $table->integer('item_height');
            $table->integer('sheet_width');
            $table->integer('sheet_height');
            $table->integer('items_per_sheet');
            $table->integer('sheets_required');

            $table->decimal('material_cost', 10, 2);
            $table->decimal('production_cost', 10, 2);
            $table->decimal('setup_cost', 10, 2);
            $table->decimal('markup_percentage', 5, 2)->default(0);
            $table->decimal('total_cost', 10, 2);
            $table->decimal('final_price', 10, 2);

            $table->json('imposition_layout')->nullable();
            $table->timestamps();
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('estimate_parts');
        Schema::dropIfExists('estimates');
    }
};
