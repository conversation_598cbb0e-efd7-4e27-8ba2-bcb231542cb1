<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('ink_and_toner', function (Blueprint $table) {
            $table->decimal('consumption_rate_kg_per_m2', 8, 6)->default(0.0015); // Default 1.5g per m²
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('ink_and_toner', function (Blueprint $table) {
            $table->dropColumn('consumption_rate_kg_per_m2');
        });
    }
};
