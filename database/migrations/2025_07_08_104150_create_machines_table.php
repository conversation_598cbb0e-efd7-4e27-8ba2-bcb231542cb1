<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('machines', function (Blueprint $table) {
            $table->id();
            $table->foreignId('production_method_id')->constrained()->onDelete('cascade');
            $table->string('name'); // e.g., 'HP Indigo 7900', 'Heidelberg XL 106'
            $table->string('model')->nullable();
            $table->decimal('hourly_rate', 10, 2); // Cost per hour to run this machine
            $table->integer('max_sheet_width')->nullable(); // in mm
            $table->integer('max_sheet_height')->nullable(); // in mm
            $table->integer('min_sheet_width')->nullable(); // in mm
            $table->integer('min_sheet_height')->nullable(); // in mm
            $table->integer('sheets_per_hour')->nullable(); // Production speed
            $table->decimal('waste_percentage', 5, 2)->default(5.00); // Expected waste %
            $table->boolean('is_active')->default(true);
            $table->text('notes')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('machines');
    }
};
