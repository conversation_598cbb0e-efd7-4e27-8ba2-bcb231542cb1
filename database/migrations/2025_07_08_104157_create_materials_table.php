<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('materials', function (Blueprint $table) {
            $table->id();
            $table->string('name'); // e.g., '350gsm Silk', '130gsm Gloss', 'Banner Vinyl'
            $table->string('type'); // e.g., 'paper', 'vinyl', 'fabric'
            $table->string('finish')->nullable(); // e.g., 'gloss', 'matt', 'silk'
            $table->integer('weight')->nullable(); // GSM for paper, or thickness for other materials
            $table->decimal('cost_per_sheet', 10, 4)->nullable(); // Cost per sheet
            $table->decimal('cost_per_sqm', 10, 4)->nullable(); // Cost per square meter
            $table->integer('sheet_width')->nullable(); // Standard sheet width in mm
            $table->integer('sheet_height')->nullable(); // Standard sheet height in mm
            $table->boolean('is_roll')->default(false); // Is this a roll material?
            $table->integer('roll_width')->nullable(); // Roll width in mm
            $table->decimal('cost_per_linear_meter', 10, 4)->nullable(); // For roll materials
            $table->boolean('is_active')->default(true);
            $table->text('notes')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('materials');
    }
};
