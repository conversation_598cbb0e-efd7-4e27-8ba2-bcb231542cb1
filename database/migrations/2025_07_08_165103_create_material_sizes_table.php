<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('material_sizes', function (Blueprint $table) {
            $table->id();
            $table->foreignId('material_id')->constrained()->cascadeOnDelete();
            $table->string('name'); // e.g., "A4", "SRA3", "Custom 700x1000"
            $table->integer('width_mm');
            $table->integer('height_mm');
            $table->decimal('cost_per_sheet', 8, 4)->nullable();
            $table->decimal('cost_per_sqm', 8, 4)->nullable();
            $table->decimal('cost_per_linear_meter', 8, 4)->nullable(); // For rolls
            $table->integer('minimum_quantity')->default(1);
            $table->boolean('is_standard_size')->default(false); // A4, A3, etc.
            $table->boolean('is_available')->default(true);
            $table->text('notes')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('material_sizes');
    }
};
