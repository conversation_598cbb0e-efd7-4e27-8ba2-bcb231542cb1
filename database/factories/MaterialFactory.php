<?php

namespace Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;
use App\Models\MaterialType;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Material>
 */
class MaterialFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        // Create material type if it doesn't exist
        $sheetType = MaterialType::firstOrCreate(
            ['slug' => 'sheet'],
            [
                'name' => 'Sheet',
                'description' => 'Sheet-based materials',
                'active' => true,
                'sort_order' => 1,
            ]
        );

        return [
            'material_type_id' => $sheetType->id,
            'name' => $this->faker->words(2, true) . ' Paper',
            'finish' => $this->faker->randomElement(['gloss', 'matt', 'silk', 'uncoated']),
            'weight' => $this->faker->randomElement([130, 170, 250, 350]),
            'caliper' => $this->faker->numberBetween(100, 400),
            'cost_per_ton' => $this->faker->numberBetween(1000, 2000),
            'sheet_width' => 700,
            'sheet_height' => 1000,
            'is_active' => true,
            'notes' => $this->faker->sentence(),
        ];
    }

    public function roll(): static
    {
        $rollType = MaterialType::firstOrCreate(
            ['slug' => 'roll'],
            [
                'name' => 'Roll',
                'description' => 'Roll-based materials',
                'active' => true,
                'sort_order' => 2,
            ]
        );

        return $this->state(fn (array $attributes) => [
            'material_type_id' => $rollType->id,
            'name' => $this->faker->words(2, true) . ' Roll',
            'roll_width' => $this->faker->randomElement([1370, 1520, 1118]),
            'roll_cost' => $this->faker->numberBetween(300, 700),
            'roll_length_meters' => 30,
            'sheet_width' => null,
            'sheet_height' => null,
            'cost_per_ton' => null,
        ]);
    }
}
