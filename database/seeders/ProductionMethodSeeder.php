<?php

namespace Database\Seeders;

use App\Models\ProductionMethod;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class ProductionMethodSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $methods = [
            [
                'name' => 'Digital Print',
                'slug' => 'digital',
                'description' => 'High-quality digital printing for short to medium runs',
                'setup_cost' => 25.00,
                'is_active' => true,
                'capabilities' => [
                    'max_colors' => 'unlimited',
                    'variable_data' => true,
                    'quick_turnaround' => true,
                ],
            ],
            [
                'name' => 'Litho Print',
                'slug' => 'litho',
                'description' => 'Traditional offset lithographic printing for large runs',
                'setup_cost' => 150.00,
                'is_active' => true,
                'capabilities' => [
                    'max_colors' => 8,
                    'spot_colors' => true,
                    'high_volume' => true,
                    'cost_effective_large_runs' => true,
                ],
            ],
            [
                'name' => 'Wide Format',
                'slug' => 'wide-format',
                'description' => 'Large format printing for banners, posters, and signage',
                'setup_cost' => 35.00,
                'is_active' => true,
                'capabilities' => [
                    'max_width' => 3200,
                    'outdoor_materials' => true,
                    'large_scale' => true,
                ],
            ],
        ];

        foreach ($methods as $method) {
            ProductionMethod::updateOrCreate(
                ['slug' => $method['slug']],
                $method
            );
        }
    }
}
