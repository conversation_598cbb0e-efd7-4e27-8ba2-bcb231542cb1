<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class SizeSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $sizes = [
            // A Series (ISO 216)
            ['name' => 'A0', 'width_mm' => 841, 'height_mm' => 1189, 'sort_order' => 10],
            ['name' => 'A1', 'width_mm' => 594, 'height_mm' => 841, 'sort_order' => 11],
            ['name' => 'A2', 'width_mm' => 420, 'height_mm' => 594, 'sort_order' => 12],
            ['name' => 'A3', 'width_mm' => 297, 'height_mm' => 420, 'sort_order' => 13],
            ['name' => 'A4', 'width_mm' => 210, 'height_mm' => 297, 'sort_order' => 14],
            ['name' => 'A5', 'width_mm' => 148, 'height_mm' => 210, 'sort_order' => 15],
            ['name' => 'A6', 'width_mm' => 105, 'height_mm' => 148, 'sort_order' => 16],

            // B Series (ISO 216)
            ['name' => 'B0', 'width_mm' => 1000, 'height_mm' => 1414, 'sort_order' => 20],
            ['name' => 'B1', 'width_mm' => 707, 'height_mm' => 1000, 'sort_order' => 21],
            ['name' => 'B2', 'width_mm' => 500, 'height_mm' => 707, 'sort_order' => 22],
            ['name' => 'B3', 'width_mm' => 353, 'height_mm' => 500, 'sort_order' => 23],
            ['name' => 'B4', 'width_mm' => 250, 'height_mm' => 353, 'sort_order' => 24],
            ['name' => 'B5', 'width_mm' => 176, 'height_mm' => 250, 'sort_order' => 25],

            // SRA Series (ISO 12625)
            ['name' => 'SRA0', 'width_mm' => 900, 'height_mm' => 1280, 'sort_order' => 30],
            ['name' => 'SRA1', 'width_mm' => 640, 'height_mm' => 900, 'sort_order' => 31],
            ['name' => 'SRA2', 'width_mm' => 450, 'height_mm' => 640, 'sort_order' => 32],
            ['name' => 'SRA3', 'width_mm' => 320, 'height_mm' => 450, 'sort_order' => 33],
            ['name' => 'SRA4', 'width_mm' => 225, 'height_mm' => 320, 'sort_order' => 34],

            // RA Series (ISO 12625)
            ['name' => 'RA0', 'width_mm' => 860, 'height_mm' => 1220, 'sort_order' => 40],
            ['name' => 'RA1', 'width_mm' => 610, 'height_mm' => 860, 'sort_order' => 41],
            ['name' => 'RA2', 'width_mm' => 430, 'height_mm' => 610, 'sort_order' => 42],
            ['name' => 'RA3', 'width_mm' => 305, 'height_mm' => 430, 'sort_order' => 43],
            ['name' => 'RA4', 'width_mm' => 215, 'height_mm' => 305, 'sort_order' => 44],

            // Common UK/US sizes
            ['name' => 'Letter', 'width_mm' => 216, 'height_mm' => 279, 'sort_order' => 50],
            ['name' => 'Legal', 'width_mm' => 216, 'height_mm' => 356, 'sort_order' => 51],
            ['name' => 'Tabloid', 'width_mm' => 279, 'height_mm' => 432, 'sort_order' => 52],

            // Common business card sizes
            ['name' => 'Business Card (UK)', 'width_mm' => 85, 'height_mm' => 55, 'sort_order' => 60],
            ['name' => 'Business Card (US)', 'width_mm' => 89, 'height_mm' => 51, 'sort_order' => 61],

            // Common envelope sizes
            ['name' => 'DL Envelope', 'width_mm' => 110, 'height_mm' => 220, 'sort_order' => 70],
            ['name' => 'C4 Envelope', 'width_mm' => 229, 'height_mm' => 324, 'sort_order' => 71],
            ['name' => 'C5 Envelope', 'width_mm' => 162, 'height_mm' => 229, 'sort_order' => 72],
        ];

        foreach ($sizes as $size) {
            \App\Models\Size::create([
                'name' => $size['name'],
                'width_mm' => $size['width_mm'],
                'height_mm' => $size['height_mm'],
                'sort_order' => $size['sort_order'],
                'is_active' => true,
                'description' => 'Standard ' . $size['name'] . ' size',
            ]);
        }
    }
}
