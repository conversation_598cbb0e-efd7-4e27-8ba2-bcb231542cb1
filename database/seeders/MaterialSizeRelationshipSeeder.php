<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\Material;
use App\Models\Size;

class MaterialSizeRelationshipSeeder extends Seeder
{
    /**
     * Run the database seeder.
     */
    public function run(): void
    {
        $materials = Material::all();
        $sizes = Size::all();

        foreach ($materials as $material) {
            // Attach all sizes to each material with default values
            foreach ($sizes as $size) {
                $material->sizes()->attach($size->id, [
                    'ream_quantity' => $material->isSheet() ? 500 : null,
                    'bulk_quantity' => $material->isSheet() ? 2500 : null,
                    'is_available' => true,
                    'created_at' => now(),
                    'updated_at' => now(),
                ]);
            }
        }

        $this->command->info('Material-Size relationships created successfully!');
    }
}
