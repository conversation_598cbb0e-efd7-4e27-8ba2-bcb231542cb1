<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class MaterialSizeSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Get existing materials
        $materials = \App\Models\Material::all();

        if ($materials->isEmpty()) {
            $this->command->warn('No materials found. Please run MaterialSeeder first.');
            return;
        }

        // Standard paper sizes with dimensions
        $standardSizes = [
            ['name' => 'A0', 'width' => 841, 'height' => 1189, 'standard' => true],
            ['name' => 'A1', 'width' => 594, 'height' => 841, 'standard' => true],
            ['name' => 'A2', 'width' => 420, 'height' => 594, 'standard' => true],
            ['name' => 'A3', 'width' => 297, 'height' => 420, 'standard' => true],
            ['name' => 'A4', 'width' => 210, 'height' => 297, 'standard' => true],
            ['name' => 'A5', 'width' => 148, 'height' => 210, 'standard' => true],
            ['name' => 'SRA0', 'width' => 900, 'height' => 1280, 'standard' => true],
            ['name' => 'SRA1', 'width' => 640, 'height' => 900, 'standard' => true],
            ['name' => 'SRA2', 'width' => 450, 'height' => 640, 'standard' => true],
            ['name' => 'SRA3', 'width' => 320, 'height' => 450, 'standard' => true],
            ['name' => 'SRA4', 'width' => 225, 'height' => 320, 'standard' => true],
        ];

        // Custom sizes for different materials
        $customSizes = [
            ['name' => 'Business Card', 'width' => 85, 'height' => 55, 'standard' => false],
            ['name' => 'DL Flyer', 'width' => 99, 'height' => 210, 'standard' => false],
            ['name' => 'A6 Postcard', 'width' => 105, 'height' => 148, 'standard' => false],
            ['name' => 'Large Format 700x1000', 'width' => 700, 'height' => 1000, 'standard' => false],
            ['name' => 'Large Format 1000x700', 'width' => 1000, 'height' => 700, 'standard' => false],
            ['name' => 'Banner 1500x500', 'width' => 1500, 'height' => 500, 'standard' => false],
        ];

        foreach ($materials as $material) {
            // Add standard sizes to all materials
            foreach ($standardSizes as $size) {
                $costPerSheet = $this->calculateCostPerSheet($size['width'], $size['height'], $material);
                $costPerSqm = $this->calculateCostPerSqm($material);

                \App\Models\MaterialSize::create([
                    'material_id' => $material->id,
                    'name' => $size['name'],
                    'width_mm' => $size['width'],
                    'height_mm' => $size['height'],
                    'cost_per_sheet' => $costPerSheet,
                    'cost_per_sqm' => $costPerSqm,
                    'cost_per_linear_meter' => $material->is_roll ? $this->calculateCostPerLinearMeter($material) : null,
                    'minimum_quantity' => 1,
                    'is_standard_size' => $size['standard'],
                    'is_available' => true,
                ]);
            }

            // Add some custom sizes
            foreach (array_slice($customSizes, 0, 3) as $size) {
                $costPerSheet = $this->calculateCostPerSheet($size['width'], $size['height'], $material);
                $costPerSqm = $this->calculateCostPerSqm($material);

                \App\Models\MaterialSize::create([
                    'material_id' => $material->id,
                    'name' => $size['name'],
                    'width_mm' => $size['width'],
                    'height_mm' => $size['height'],
                    'cost_per_sheet' => $costPerSheet,
                    'cost_per_sqm' => $costPerSqm,
                    'cost_per_linear_meter' => $material->is_roll ? $this->calculateCostPerLinearMeter($material) : null,
                    'minimum_quantity' => $size['name'] === 'Business Card' ? 250 : 1,
                    'is_standard_size' => $size['standard'],
                    'is_available' => true,
                    'notes' => $size['name'] === 'Business Card' ? 'Minimum order 250 cards' : null,
                ]);
            }
        }
    }

    private function calculateCostPerSheet(int $width, int $height, $material): float
    {
        // Calculate area in square metres
        $areaSqm = ($width * $height) / 1000000;

        // Base cost varies by material type
        $baseCostPerSqm = match($material->name) {
            'Standard Paper 80gsm' => 0.15,
            'Premium Paper 120gsm' => 0.25,
            'Cardstock 300gsm' => 0.45,
            'Vinyl Sticker Material' => 1.20,
            'Canvas 380gsm' => 2.50,
            default => 0.20,
        };

        return round($areaSqm * $baseCostPerSqm, 4);
    }

    private function calculateCostPerSqm($material): float
    {
        return match($material->name) {
            'Standard Paper 80gsm' => 0.15,
            'Premium Paper 120gsm' => 0.25,
            'Cardstock 300gsm' => 0.45,
            'Vinyl Sticker Material' => 1.20,
            'Canvas 380gsm' => 2.50,
            default => 0.20,
        };
    }

    private function calculateCostPerLinearMeter($material): ?float
    {
        if (!$material->is_roll) {
            return null;
        }

        return match($material->name) {
            'Vinyl Sticker Material' => 1.50,
            'Canvas 380gsm' => 3.00,
            default => 1.00,
        };
    }
}
