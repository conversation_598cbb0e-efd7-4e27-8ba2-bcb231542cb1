<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\MaterialType;

class MaterialTypeSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $materialTypes = [
            [
                'name' => 'Sheet',
                'slug' => 'sheet',
                'description' => 'Sheet-based materials that come in fixed sizes (e.g., paper, card, vinyl sheets)',
                'sort_order' => 1,
                'active' => true,
            ],
            [
                'name' => 'Roll',
                'slug' => 'roll',
                'description' => 'Roll-based materials sold by linear meter or area (e.g., vinyl rolls, fabric rolls)',
                'sort_order' => 2,
                'active' => true,
            ],
            [
                'name' => 'Packaging',
                'slug' => 'packaging',
                'description' => 'Packaging materials and supplies (e.g., boxes, bags, protective materials)',
                'sort_order' => 3,
                'active' => true,
            ],
        ];

        foreach ($materialTypes as $materialType) {
            MaterialType::updateOrCreate(
                ['slug' => $materialType['slug']],
                $materialType
            );
        }
    }
}
