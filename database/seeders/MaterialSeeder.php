<?php

namespace Database\Seeders;

use App\Models\Material;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class MaterialSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Get material types
        $sheetType = \App\Models\MaterialType::where('slug', 'sheet')->first();
        $rollType = \App\Models\MaterialType::where('slug', 'roll')->first();

        $materials = [
            // Paper Materials (Sheets)
            [
                'material_type_id' => $sheetType?->id,
                'name' => '130gsm Gloss',
                'finish' => 'gloss',
                'weight' => 130,
                'cost_per_ton' => 1200,
                'sheet_width' => 700,
                'sheet_height' => 1000,
                'is_active' => true,
                'notes' => 'Standard gloss coated paper',
            ],
            [
                'material_type_id' => $sheetType?->id,
                'name' => '170gsm Silk',
                'finish' => 'silk',
                'weight' => 170,
                'cost_per_ton' => 1600,
                'sheet_width' => 700,
                'sheet_height' => 1000,
                'is_active' => true,
                'notes' => 'Premium silk finish paper',
            ],
            [
                'material_type_id' => $sheetType?->id,
                'name' => '250gsm Matt',
                'finish' => 'matt',
                'weight' => 250,
                'cost_per_ton' => 1800,
                'sheet_width' => 700,
                'sheet_height' => 1000,
                'is_active' => true,
                'notes' => 'Heavy matt coated paper',
            ],
            [
                'material_type_id' => $sheetType?->id,
                'name' => '350gsm Uncoated',
                'finish' => 'uncoated',
                'weight' => 350,
                'cost_per_ton' => 1400,
                'sheet_width' => 700,
                'sheet_height' => 1000,
                'is_active' => true,
                'notes' => 'Thick uncoated cardstock',
            ],
            // Roll Materials for Wide Format
            [
                'material_type_id' => $rollType?->id,
                'name' => 'Banner Vinyl',
                'finish' => 'matt',
                'weight' => 440,
                'roll_width' => 1370,
                'roll_cost' => 350,
                'roll_length_meters' => 30,
                'is_active' => true,
                'notes' => 'PVC banner material for outdoor use',
            ],
            [
                'material_type_id' => $rollType?->id,
                'name' => 'Photo Paper Roll',
                'finish' => 'gloss',
                'weight' => 260,
                'roll_width' => 1118,
                'roll_cost' => 450,
                'roll_length_meters' => 30,
                'is_active' => true,
                'notes' => 'High-quality photo paper for posters',
            ],
            [
                'material_type_id' => $rollType?->id,
                'name' => 'Canvas Roll',
                'finish' => 'matt',
                'weight' => 380,
                'roll_width' => 1520,
                'roll_cost' => 680,
                'roll_length_meters' => 30,
                'is_active' => true,
                'notes' => 'Canvas material for art reproductions',
            ],
            [
                'material_type_id' => $rollType?->id,
                'name' => 'Adhesive Vinyl',
                'finish' => 'gloss',
                'weight' => 80,
                'roll_width' => 1370,
                'roll_cost' => 280,
                'roll_length_meters' => 30,
                'is_active' => true,
                'notes' => 'Self-adhesive vinyl for decals and signs',
            ],
        ];

        foreach ($materials as $material) {
            Material::updateOrCreate(
                ['name' => $material['name']],
                $material
            );
        }
    }
}
