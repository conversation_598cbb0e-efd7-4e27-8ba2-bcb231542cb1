<?php

namespace Database\Seeders;

use App\Models\Product;
use Illuminate\Database\Seeder;

class ProductSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $products = [
            // Flat Products
            [
                'name' => 'Business Cards',
                'category' => 'flat',
                'description' => 'Standard business cards, single or double sided',
                'specifications' => [
                    'standard_sizes' => ['85x55', '90x50'],
                    'typical_materials' => ['350gsm Card', '400gsm Card'],
                    'finishing_options' => ['matt_lamination', 'gloss_lamination', 'spot_uv']
                ],
                'requires_cutting' => true,
                'sort_order' => 10,
            ],
            [
                'name' => 'Flyers',
                'category' => 'flat',
                'description' => 'Single sheet promotional materials',
                'specifications' => [
                    'standard_sizes' => ['A6', 'A5', 'A4', 'DL'],
                    'typical_materials' => ['130gsm Gloss', '170gsm Gloss', '250gsm Card'],
                ],
                'requires_cutting' => true,
                'sort_order' => 20,
            ],
            [
                'name' => 'Posters',
                'category' => 'flat',
                'description' => 'Large format promotional materials',
                'specifications' => [
                    'standard_sizes' => ['A3', 'A2', 'A1', 'A0'],
                    'typical_materials' => ['130gsm Gloss', '170gsm Gloss', '200gsm Satin'],
                ],
                'requires_cutting' => true,
                'sort_order' => 30,
            ],
            [
                'name' => 'Stickers/Labels',
                'category' => 'flat',
                'description' => 'Adhesive labels and stickers',
                'specifications' => [
                    'typical_materials' => ['Vinyl', 'Paper Labels', 'Clear Labels'],
                    'finishing_options' => ['die_cutting', 'kiss_cutting']
                ],
                'requires_cutting' => true,
                'setup_cost_multiplier' => 1.5,
                'sort_order' => 40,
            ],

            // Folded Products
            [
                'name' => 'Leaflets',
                'category' => 'folded',
                'description' => 'Folded promotional materials',
                'specifications' => [
                    'fold_types' => ['half_fold', 'tri_fold', 'z_fold', 'gate_fold'],
                    'standard_sizes' => ['A4', 'A5', 'DL'],
                    'typical_materials' => ['130gsm Gloss', '170gsm Gloss'],
                ],
                'requires_folding' => true,
                'requires_cutting' => true,
                'setup_cost_multiplier' => 1.2,
                'sort_order' => 50,
            ],
            [
                'name' => 'Greeting Cards',
                'category' => 'folded',
                'description' => 'Folded greeting cards',
                'specifications' => [
                    'fold_types' => ['half_fold'],
                    'standard_sizes' => ['A6', 'A5', 'Square'],
                    'typical_materials' => ['300gsm Card', '350gsm Card'],
                    'finishing_options' => ['matt_lamination', 'spot_uv', 'foiling']
                ],
                'requires_folding' => true,
                'requires_cutting' => true,
                'setup_cost_multiplier' => 1.3,
                'sort_order' => 60,
            ],

            // Bound Products
            [
                'name' => 'Saddle Stitched Booklets',
                'category' => 'bound',
                'description' => 'Booklets bound with saddle stitching',
                'specifications' => [
                    'binding_types' => ['saddle_stitch'],
                    'page_range' => [4, 64],
                    'typical_materials' => ['130gsm Gloss', '170gsm Gloss'],
                    'cover_materials' => ['250gsm Card', '300gsm Card']
                ],
                'requires_binding' => true,
                'requires_cutting' => true,
                'min_pages' => 4,
                'max_pages' => 64,
                'setup_cost_multiplier' => 1.8,
                'sort_order' => 70,
            ],
            [
                'name' => 'Perfect Bound Books',
                'category' => 'bound',
                'description' => 'Books bound with perfect binding',
                'specifications' => [
                    'binding_types' => ['perfect_bound'],
                    'page_range' => [32, 500],
                    'typical_materials' => ['80gsm Offset', '90gsm Offset', '115gsm Gloss'],
                    'cover_materials' => ['250gsm Card', '300gsm Card']
                ],
                'requires_binding' => true,
                'requires_cutting' => true,
                'min_pages' => 32,
                'max_pages' => 500,
                'setup_cost_multiplier' => 2.2,
                'sort_order' => 80,
            ],
            [
                'name' => 'Wire-O Bound',
                'category' => 'bound',
                'description' => 'Documents bound with wire-o binding',
                'specifications' => [
                    'binding_types' => ['wire_o'],
                    'page_range' => [2, 200],
                    'typical_materials' => ['80gsm Offset', '100gsm Offset'],
                    'cover_materials' => ['250gsm Card', '300gsm Card']
                ],
                'requires_binding' => true,
                'requires_cutting' => true,
                'min_pages' => 2,
                'max_pages' => 200,
                'setup_cost_multiplier' => 1.6,
                'sort_order' => 90,
            ],

            // Specialty Products
            [
                'name' => 'NCR Forms',
                'category' => 'specialty',
                'description' => 'No Carbon Required duplicate/triplicate forms',
                'specifications' => [
                    'parts' => [2, 3, 4],
                    'typical_materials' => ['NCR White', 'NCR Pink', 'NCR Yellow', 'NCR Blue'],
                    'binding_options' => ['glued_pad', 'individual_sets']
                ],
                'requires_cutting' => true,
                'setup_cost_multiplier' => 2.0,
                'sort_order' => 100,
            ],
            [
                'name' => 'Presentation Folders',
                'category' => 'specialty',
                'description' => 'Die-cut presentation folders',
                'specifications' => [
                    'typical_materials' => ['350gsm Card', '400gsm Card'],
                    'finishing_options' => ['matt_lamination', 'gloss_lamination', 'spot_uv', 'foiling'],
                    'die_cutting' => true
                ],
                'requires_cutting' => true,
                'setup_cost_multiplier' => 3.0,
                'sort_order' => 110,
            ],
        ];

        foreach ($products as $productData) {
            Product::create($productData);
        }
    }
}
