<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class CustomerSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create sample customers with contacts and addresses
        $customers = [
            [
                'name' => 'ABC Marketing Ltd',
                'company_number' => '12345678',
                'vat_number' => 'GB123456789',
                'description' => 'Full-service marketing agency specialising in print and digital campaigns.',
                'website' => 'https://abcmarketing.co.uk',
                'status' => 'active',
                'credit_limit' => 5000.00,
                'payment_terms' => '30_days',
                'notes' => 'Long-standing client, always pays on time.',
                'contacts' => [
                    [
                        'first_name' => 'Sarah',
                        'last_name' => 'Johnson',
                        'job_title' => 'Marketing Director',
                        'email' => '<EMAIL>',
                        'phone' => '020 7123 4567',
                        'mobile' => '07700 123456',
                        'is_primary' => true,
                        'receives_quotes' => true,
                        'receives_invoices' => true,
                    ],
                    [
                        'first_name' => 'Mike',
                        'last_name' => '<PERSON>',
                        'job_title' => 'Print Coordinator',
                        'email' => '<EMAIL>',
                        'phone' => '020 7123 4568',
                        'is_primary' => false,
                        'receives_quotes' => true,
                        'receives_invoices' => false,
                    ],
                ],
                'addresses' => [
                    [
                        'type' => 'both',
                        'label' => 'Head Office',
                        'company_name' => 'ABC Marketing Ltd',
                        'address_line_1' => '123 High Street',
                        'address_line_2' => 'Suite 45',
                        'city' => 'London',
                        'county' => 'Greater London',
                        'postcode' => 'SW1A 1AA',
                        'country' => 'United Kingdom',
                        'is_default' => true,
                    ],
                ],
            ],
            [
                'name' => 'XYZ Events Company',
                'company_number' => '87654321',
                'vat_number' => 'GB987654321',
                'description' => 'Corporate events and conference management.',
                'website' => 'https://xyzevents.com',
                'status' => 'active',
                'credit_limit' => 3000.00,
                'payment_terms' => '14_days',
                'notes' => 'Seasonal business, busy in Q4.',
                'contacts' => [
                    [
                        'first_name' => 'Emma',
                        'last_name' => 'Davis',
                        'job_title' => 'Operations Manager',
                        'email' => '<EMAIL>',
                        'phone' => '0161 234 5678',
                        'mobile' => '************',
                        'is_primary' => true,
                        'receives_quotes' => true,
                        'receives_invoices' => true,
                    ],
                ],
                'addresses' => [
                    [
                        'type' => 'billing',
                        'label' => 'Billing Address',
                        'company_name' => 'XYZ Events Company',
                        'address_line_1' => '456 Business Park',
                        'city' => 'Manchester',
                        'county' => 'Greater Manchester',
                        'postcode' => 'M1 2AB',
                        'country' => 'United Kingdom',
                        'is_default' => true,
                    ],
                    [
                        'type' => 'delivery',
                        'label' => 'Warehouse',
                        'company_name' => 'XYZ Events Company',
                        'address_line_1' => '789 Industrial Estate',
                        'city' => 'Manchester',
                        'county' => 'Greater Manchester',
                        'postcode' => 'M2 3CD',
                        'country' => 'United Kingdom',
                        'is_default' => true,
                        'delivery_instructions' => 'Use loading bay 3. Contact security on arrival.',
                    ],
                ],
            ],
            [
                'name' => 'Local Restaurant Group',
                'status' => 'prospect',
                'credit_limit' => 1000.00,
                'payment_terms' => '7_days',
                'description' => 'Chain of local restaurants requiring menu printing.',
                'notes' => 'Potential new client, quoted for menu printing.',
                'contacts' => [
                    [
                        'first_name' => 'James',
                        'last_name' => 'Wilson',
                        'job_title' => 'General Manager',
                        'email' => '<EMAIL>',
                        'phone' => '0114 567 8901',
                        'is_primary' => true,
                        'receives_quotes' => true,
                        'receives_invoices' => false,
                    ],
                ],
                'addresses' => [
                    [
                        'type' => 'delivery',
                        'label' => 'Main Restaurant',
                        'company_name' => 'Local Restaurant Group',
                        'address_line_1' => '321 Food Street',
                        'city' => 'Sheffield',
                        'county' => 'South Yorkshire',
                        'postcode' => 'S1 4EF',
                        'country' => 'United Kingdom',
                        'is_default' => true,
                    ],
                ],
            ],
        ];

        foreach ($customers as $customerData) {
            $contacts = $customerData['contacts'];
            $addresses = $customerData['addresses'];
            unset($customerData['contacts'], $customerData['addresses']);

            $customer = \App\Models\Customer::create($customerData);

            foreach ($contacts as $contactData) {
                $customer->contacts()->create($contactData);
            }

            foreach ($addresses as $addressData) {
                $customer->addresses()->create($addressData);
            }
        }
    }
}
