<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Print Estimate - {{ $estimate->job_title }}</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
        }
        .header {
            background-color: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            text-align: center;
        }
        .estimate-details {
            background-color: #fff;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
        }
        .detail-row {
            display: flex;
            justify-content: space-between;
            padding: 8px 0;
            border-bottom: 1px solid #f1f3f4;
        }
        .detail-row:last-child {
            border-bottom: none;
        }
        .detail-label {
            font-weight: bold;
            color: #495057;
        }
        .detail-value {
            color: #212529;
        }
        .price-highlight {
            background-color: #e7f3ff;
            padding: 15px;
            border-radius: 8px;
            text-align: center;
            margin: 20px 0;
        }
        .price-amount {
            font-size: 24px;
            font-weight: bold;
            color: #0066cc;
        }
        .actions {
            text-align: center;
            margin: 30px 0;
        }
        .btn {
            display: inline-block;
            padding: 12px 24px;
            margin: 0 10px;
            text-decoration: none;
            border-radius: 6px;
            font-weight: bold;
            text-align: center;
        }
        .btn-primary {
            background-color: #007bff;
            color: white;
        }
        .btn-success {
            background-color: #28a745;
            color: white;
        }
        .btn-secondary {
            background-color: #6c757d;
            color: white;
        }
        .footer {
            margin-top: 30px;
            padding-top: 20px;
            border-top: 1px solid #dee2e6;
            font-size: 14px;
            color: #6c757d;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>Print Estimate</h1>
        <p>{{ $estimate->job_title }}</p>
        <p><strong>Estimate #:</strong> {{ $estimate->estimate_number }}</p>
    </div>

    <div class="estimate-details">
        <h2>Project Details</h2>
        
        <div class="detail-row">
            <span class="detail-label">Client:</span>
            <span class="detail-value">{{ $estimate->client_name }}</span>
        </div>
        
        @if($estimate->customer)
        <div class="detail-row">
            <span class="detail-label">Company:</span>
            <span class="detail-value">{{ $estimate->customer->name }}</span>
        </div>
        @endif
        
        <div class="detail-row">
            <span class="detail-label">Job Title:</span>
            <span class="detail-value">{{ $estimate->job_title }}</span>
        </div>
        
        @if($estimate->description)
        <div class="detail-row">
            <span class="detail-label">Description:</span>
            <span class="detail-value">{{ $estimate->description }}</span>
        </div>
        @endif
        
        <div class="detail-row">
            <span class="detail-label">Quantity:</span>
            <span class="detail-value">{{ number_format($estimate->total_quantity) }} items</span>
        </div>
        
        <div class="detail-row">
            <span class="detail-label">Item Size:</span>
            <span class="detail-value">{{ $estimate->item_width }}mm × {{ $estimate->item_height }}mm</span>
        </div>
        
        <div class="detail-row">
            <span class="detail-label">Material:</span>
            <span class="detail-value">{{ $estimate->material->display_name }}</span>
        </div>
        
        @if($estimate->materialSize)
        <div class="detail-row">
            <span class="detail-label">Sheet Size:</span>
            <span class="detail-value">{{ $estimate->materialSize->display_name }}</span>
        </div>
        @endif
        
        <div class="detail-row">
            <span class="detail-label">Production Method:</span>
            <span class="detail-value">{{ $estimate->productionMethod->name }}</span>
        </div>
        
        <div class="detail-row">
            <span class="detail-label">Machine:</span>
            <span class="detail-value">{{ $estimate->machine->name }}</span>
        </div>
        
        @if($estimate->has_versions && $estimate->versions)
        <div class="detail-row">
            <span class="detail-label">Versions:</span>
            <span class="detail-value">
                @foreach($estimate->versions as $version)
                    {{ $version['name'] }} ({{ number_format($version['quantity']) }})
                    @if(!$loop->last), @endif
                @endforeach
            </span>
        </div>
        @endif
    </div>

    <div class="price-highlight">
        <p style="margin: 0; font-size: 18px;">Total Price</p>
        <div class="price-amount">£{{ number_format($estimate->final_price, 2) }}</div>
        <p style="margin: 5px 0 0 0; font-size: 14px; color: #666;">
            (£{{ number_format($estimate->final_price / $estimate->total_quantity, 4) }} per item)
        </p>
    </div>

    <div class="actions">
        <a href="{{ $publicUrl }}" class="btn btn-primary">View Full Estimate</a>
    </div>

    <div class="estimate-details">
        <h3>Cost Breakdown</h3>
        
        <div class="detail-row">
            <span class="detail-label">Material Cost:</span>
            <span class="detail-value">£{{ number_format($estimate->material_cost, 2) }}</span>
        </div>
        
        <div class="detail-row">
            <span class="detail-label">Production Cost:</span>
            <span class="detail-value">£{{ number_format($estimate->production_cost, 2) }}</span>
        </div>
        
        <div class="detail-row">
            <span class="detail-label">Setup Cost:</span>
            <span class="detail-value">£{{ number_format($estimate->setup_cost, 2) }}</span>
        </div>
        
        <div class="detail-row">
            <span class="detail-label">Subtotal:</span>
            <span class="detail-value">£{{ number_format($estimate->total_cost, 2) }}</span>
        </div>
        
        <div class="detail-row">
            <span class="detail-label">Markup ({{ $estimate->markup_percentage }}%):</span>
            <span class="detail-value">£{{ number_format($estimate->final_price - $estimate->total_cost, 2) }}</span>
        </div>
        
        <div class="detail-row" style="font-weight: bold; font-size: 16px; border-top: 2px solid #007bff; padding-top: 10px;">
            <span class="detail-label">Final Price:</span>
            <span class="detail-value">£{{ number_format($estimate->final_price, 2) }}</span>
        </div>
    </div>

    <div class="footer">
        <p>This estimate is valid for 30 days from the date of issue.</p>
        <p>To accept or decline this estimate, please click the "View Full Estimate" button above.</p>
        <p>If you have any questions, please don't hesitate to contact us.</p>
    </div>
</body>
</html>
