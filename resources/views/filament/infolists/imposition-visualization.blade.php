@php
    $imposition = $getRecord()->imposition_layout;
@endphp

<div class="space-y-6">
    @if($imposition && isset($imposition['positions']) && count($imposition['positions']) > 0)
        <!-- Imposition Layout Visualization -->
        <div class="bg-gray-50 rounded-lg p-6 border">
            <h4 class="text-lg font-semibold mb-4 text-gray-900">Layout Visualisation</h4>

            @include('filament.infolists.imposition-visualization-content', ['imposition' => $imposition])
        </div>

        <!-- Version Breakdown -->
        @if(isset($imposition['versions']) && count($imposition['versions']) > 1)
            <div class="bg-gray-50 rounded-lg p-4 border">
                <div class="flex justify-between items-center mb-4">
                    <h4 class="text-lg font-semibold text-gray-900">Version Distribution</h4>
                    <span class="text-sm px-3 py-1 bg-gray-200 text-gray-700 rounded-full">
                        {{ ucfirst($imposition['layout_mode'] ?? 'ganged') }} Layout
                    </span>
                </div>
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                    @foreach($imposition['version_distribution'] as $index => $version)
                        @php
                            $versionColors = [
                                0 => ['bg' => 'bg-blue-50', 'border' => 'border-blue-200', 'text' => 'text-blue-900'],
                                1 => ['bg' => 'bg-green-50', 'border' => 'border-green-200', 'text' => 'text-green-900'],
                                2 => ['bg' => 'bg-purple-50', 'border' => 'border-purple-200', 'text' => 'text-purple-900'],
                                3 => ['bg' => 'bg-orange-50', 'border' => 'border-orange-200', 'text' => 'text-orange-900'],
                                4 => ['bg' => 'bg-red-50', 'border' => 'border-red-200', 'text' => 'text-red-900'],
                            ];
                            $colors = $versionColors[$index % 5];
                        @endphp
                        <div class="{{ $colors['bg'] }} {{ $colors['border'] }} border rounded-lg p-3">
                            <div class="font-medium {{ $colors['text'] }} mb-1">{{ $version['name'] }}</div>
                            <div class="text-sm space-y-1">
                                <div class="flex justify-between">
                                    <span class="text-gray-600">Quantity:</span>
                                    <span class="font-medium">{{ $version['quantity'] }}</span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-gray-600">Percentage:</span>
                                    <span class="font-medium">{{ number_format($version['percentage'], 1) }}%</span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-gray-600">Sheets needed:</span>
                                    <span class="font-medium">{{ $version['sheets_needed'] }}</span>
                                </div>
                                @if(isset($version['sheet_start']))
                                    <div class="flex justify-between">
                                        <span class="text-gray-600">Sheet range:</span>
                                        <span class="font-medium">{{ $version['sheet_start'] }}-{{ $version['sheet_end'] }}</span>
                                    </div>
                                @endif
                            </div>
                        </div>
                    @endforeach
                </div>

                @if(($imposition['layout_mode'] ?? 'ganged') === 'separate')
                    <div class="mt-4 p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
                        <div class="flex items-center">
                            <svg class="w-5 h-5 text-yellow-600 mr-2" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path>
                            </svg>
                            <div>
                                <div class="font-medium text-yellow-800">Separate Sheet Layout</div>
                                <div class="text-sm text-yellow-700">Each version will be printed on separate sheets. This may be less efficient but provides better organization for finishing.</div>
                            </div>
                        </div>
                    </div>
                @endif
            </div>
        @endif

        <!-- Additional Production Information -->
        <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div class="bg-blue-50 rounded-lg p-4 border border-blue-200">
                <div class="flex items-center">
                    <div class="bg-blue-500 rounded-full p-2 mr-3">
                        <svg class="w-5 h-5 text-white" fill="currentColor" viewBox="0 0 20 20">
                            <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                    </div>
                    <div>
                        <div class="text-sm text-blue-600 font-medium">Layout Efficiency</div>
                        <div class="text-2xl font-bold text-blue-900">{{ number_format($imposition['efficiency_percentage'], 1) }}%</div>
                    </div>
                </div>
            </div>
            
            <div class="bg-green-50 rounded-lg p-4 border border-green-200">
                <div class="flex items-center">
                    <div class="bg-green-500 rounded-full p-2 mr-3">
                        <svg class="w-5 h-5 text-white" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M4 4a2 2 0 00-2 2v4a2 2 0 002 2V6h10a2 2 0 00-2-2H4zm2 6a2 2 0 012-2h8a2 2 0 012 2v4a2 2 0 01-2 2H8a2 2 0 01-2-2v-4zm6 4a2 2 0 100-4 2 2 0 000 4z" clip-rule="evenodd"></path>
                        </svg>
                    </div>
                    <div>
                        <div class="text-sm text-green-600 font-medium">Cost per Item</div>
                        <div class="text-2xl font-bold text-green-900">
                            @if($getRecord()->total_quantity > 0)
                                £{{ number_format($getRecord()->final_price / $getRecord()->total_quantity, 4) }}
                            @else
                                £0.0000
                            @endif
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="bg-purple-50 rounded-lg p-4 border border-purple-200">
                <div class="flex items-center">
                    <div class="bg-purple-500 rounded-full p-2 mr-3">
                        <svg class="w-5 h-5 text-white" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z" clip-rule="evenodd"></path>
                        </svg>
                    </div>
                    <div>
                        <div class="text-sm text-purple-600 font-medium">Production Time</div>
                        <div class="text-2xl font-bold text-purple-900">
                            {{ number_format($getRecord()->machine->calculateProductionTime($imposition['sheets_required']), 1) }}h
                        </div>
                    </div>
                </div>
            </div>
        </div>
    @else
        <div class="text-center py-8 text-gray-500">
            <svg class="w-12 h-12 mx-auto mb-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
            </svg>
            <p>No imposition data available for this estimate.</p>
        </div>
    @endif
</div>
