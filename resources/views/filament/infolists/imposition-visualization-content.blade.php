<div class="flex flex-col lg:flex-row gap-6">
    <!-- Visual Representation -->
    <div class="flex-1">
        <div class="relative mx-auto bg-white border-2 border-gray-400" 
             style="max-width: 500px; aspect-ratio: {{ $imposition['layout_data']['sheet_width'] }}/{{ $imposition['layout_data']['sheet_height'] }};">
            
            <!-- Items positioned on sheet -->
            @if(isset($imposition['positions_with_versions']) && count($imposition['positions_with_versions']) > 0)
                @foreach($imposition['positions_with_versions'] as $position)
                    @php
                        $versionColors = [
                            0 => ['bg' => 'rgb(219 234 254)', 'border' => 'rgb(59 130 246)', 'text' => 'rgb(30 64 175)'],
                            1 => ['bg' => 'rgb(220 252 231)', 'border' => 'rgb(34 197 94)', 'text' => 'rgb(21 128 61)'],
                            2 => ['bg' => 'rgb(243 232 255)', 'border' => 'rgb(168 85 247)', 'text' => 'rgb(107 33 168)'],
                            3 => ['bg' => 'rgb(254 215 170)', 'border' => 'rgb(251 146 60)', 'text' => 'rgb(194 65 12)'],
                            4 => ['bg' => 'rgb(254 202 202)', 'border' => 'rgb(239 68 68)', 'text' => 'rgb(185 28 28)'],
                        ];
                        $versionIndex = array_search($position['version_name'], array_column($imposition['versions'], 'name')) % 5;
                        $colors = $versionColors[$versionIndex];
                    @endphp
                    <div class="absolute flex items-center justify-center text-xs font-medium rounded-sm"
                         style="border: 2px solid {{ $colors['border'] }}; background-color: {{ $colors['bg'] }}; color: {{ $colors['text'] }};
                            left: {{ ($position['x'] / $imposition['layout_data']['sheet_width']) * 100 }}%;
                            top: {{ ($position['y'] / $imposition['layout_data']['sheet_height']) * 100 }}%;
                            width: {{ ($position['width'] / $imposition['layout_data']['sheet_width']) * 100 }}%;
                            height: {{ ($position['height'] / $imposition['layout_data']['sheet_height']) * 100 }}%;
                         "
                         title="{{ $position['version_name'] }} - Item {{ $position['version_item_number'] }}">
                        {{ substr($position['version_name'], 0, 1) }}{{ $position['version_item_number'] }}
                    </div>
                @endforeach
            @else
                @foreach($imposition['positions'] as $position)
                    <div class="absolute border border-blue-500 bg-blue-100 flex items-center justify-center text-xs font-medium text-blue-800 rounded-sm"
                         style="
                            left: {{ ($position['x'] / $imposition['layout_data']['sheet_width']) * 100 }}%;
                            top: {{ ($position['y'] / $imposition['layout_data']['sheet_height']) * 100 }}%;
                            width: {{ ($position['width'] / $imposition['layout_data']['sheet_width']) * 100 }}%;
                            height: {{ ($position['height'] / $imposition['layout_data']['sheet_height']) * 100 }}%;
                         ">
                        {{ $position['item_number'] }}
                    </div>
                @endforeach
            @endif
            
            <!-- Sheet dimensions label -->
            <div class="absolute -bottom-6 left-0 right-0 text-center text-sm text-gray-600">
                {{ $imposition['layout_data']['sheet_width'] }}mm × {{ $imposition['layout_data']['sheet_height'] }}mm
            </div>
        </div>
        
        <!-- Legend -->
        <div class="mt-8 text-sm">
            <div class="flex flex-wrap gap-4 justify-center mb-3">
                <div class="flex items-center">
                    <div class="w-4 h-4 border-2 border-gray-400 bg-white mr-2 rounded"></div>
                    <span class="text-gray-700">Sheet</span>
                </div>
                @if(!isset($imposition['versions']) || count($imposition['versions']) <= 1)
                    <div class="flex items-center">
                        <div class="w-4 h-4 border border-blue-500 bg-blue-100 mr-2 rounded-sm"></div>
                        <span class="text-gray-700">Items ({{ $imposition['layout_data']['item_width'] }}×{{ $imposition['layout_data']['item_height'] }}mm)</span>
                    </div>
                @endif
                @if($imposition['rotated'])
                    <div class="flex items-center text-orange-600">
                        <svg class="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M4 2a1 1 0 011 1v2.101a7.002 7.002 0 0111.601 2.566 1 1 0 11-1.885.666A5.002 5.002 0 005.999 7H9a1 1 0 010 2H4a1 1 0 01-1-1V3a1 1 0 011-1zm.008 9.057a1 1 0 011.276.61A5.002 5.002 0 0014.001 13H11a1 1 0 110-2h5a1 1 0 011 1v5a1 1 0 11-2 0v-2.101a7.002 7.002 0 01-11.601-2.566 1 1 0 01.61-1.276z" clip-rule="evenodd"></path>
                        </svg>
                        <span class="font-medium">Items Rotated</span>
                    </div>
                @endif
            </div>

            @if(isset($imposition['versions']) && count($imposition['versions']) > 1)
                <div class="text-center">
                    <div class="text-gray-600 mb-2 font-medium">Versions:</div>
                    <div class="flex flex-wrap gap-2 justify-center">
                        @foreach($imposition['versions'] as $index => $version)
                            @php
                                $versionColors = [
                                    0 => ['bg' => 'rgb(219 234 254)', 'border' => 'rgb(59 130 246)'],
                                    1 => ['bg' => 'rgb(220 252 231)', 'border' => 'rgb(34 197 94)'],
                                    2 => ['bg' => 'rgb(243 232 255)', 'border' => 'rgb(168 85 247)'],
                                    3 => ['bg' => 'rgb(254 215 170)', 'border' => 'rgb(251 146 60)'],
                                    4 => ['bg' => 'rgb(254 202 202)', 'border' => 'rgb(239 68 68)'],
                                ];
                                $colors = $versionColors[$index % 5];
                            @endphp
                            <div class="flex items-center">
                                <div class="w-4 h-4 mr-1 rounded-sm" style="border: 2px solid {{ $colors['border'] }}; background-color: {{ $colors['bg'] }};"></div>
                                <span class="text-xs text-gray-700">{{ $version['name'] }} ({{ $version['quantity'] }})</span>
                            </div>
                        @endforeach
                    </div>
                </div>
            @endif
        </div>
    </div>
    
    <!-- Layout Statistics -->
    <div class="lg:w-80">
        <div class="bg-white rounded-lg border p-4 space-y-4">
            <h5 class="font-semibold text-gray-900 border-b pb-2">Layout Details</h5>
            
            <div class="grid grid-cols-2 gap-3 text-sm">
                <div>
                    <span class="text-gray-600">Grid Layout:</span>
                    <div class="font-medium">{{ $imposition['items_horizontal'] }} × {{ $imposition['items_vertical'] }}</div>
                </div>
                
                <div>
                    <span class="text-gray-600">Items per Sheet:</span>
                    <div class="font-medium">{{ $imposition['items_per_sheet'] }}</div>
                </div>
                
                <div>
                    <span class="text-gray-600">Efficiency:</span>
                    <div class="font-medium text-green-600">{{ number_format($imposition['efficiency_percentage'], 1) }}%</div>
                </div>
                
                <div>
                    <span class="text-gray-600">Waste:</span>
                    <div class="font-medium text-red-600">{{ number_format(100 - $imposition['efficiency_percentage'], 1) }}%</div>
                </div>
                
                <div>
                    <span class="text-gray-600">Bleed:</span>
                    <div class="font-medium">{{ $imposition['layout_data']['bleed'] }}mm</div>
                </div>
                
                <div>
                    <span class="text-gray-600">Gutter:</span>
                    <div class="font-medium">{{ $imposition['layout_data']['gutter'] }}mm</div>
                </div>
            </div>
            
            <!-- Item Dimensions -->
            <div class="border-t pt-3">
                <span class="text-gray-600 text-sm">Item Dimensions:</span>
                <div class="font-medium">
                    {{ $imposition['layout_data']['item_width'] }}mm × {{ $imposition['layout_data']['item_height'] }}mm
                </div>
                @if($imposition['layout_data']['bleed'] > 0)
                    <div class="text-sm text-gray-500">
                        With bleed: {{ $imposition['layout_data']['item_with_bleed_width'] }}mm × {{ $imposition['layout_data']['item_with_bleed_height'] }}mm
                    </div>
                @endif
            </div>
            
            <!-- Production Info -->
            <div class="border-t pt-3">
                <span class="text-gray-600 text-sm">Production:</span>
                <div class="space-y-1 text-sm">
                    <div>
                        <span class="text-gray-500">Sheets needed:</span>
                        <span class="font-medium">{{ $imposition['sheets_required'] }}</span>
                    </div>
                    <div>
                        <span class="text-gray-500">Total items:</span>
                        <span class="font-medium">{{ $imposition['total_quantity'] ?? ($imposition['items_per_sheet'] * $imposition['sheets_required']) }}</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
