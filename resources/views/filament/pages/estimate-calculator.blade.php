<x-filament-panels::page>
    <div class="space-y-6">
        <!-- Form Section -->
        <div class="bg-white rounded-lg shadow p-6">
            <form wire:submit="calculate">
                {{ $this->form }}

                <div class="mt-6 flex gap-3">
                    <x-filament::button type="submit" size="lg">
                        <x-heroicon-m-calculator class="w-5 h-5 mr-2" />
                        Calculate Estimate
                    </x-filament::button>

                    @if($showResults)
                        <x-filament::button
                            type="button"
                            color="gray"
                            wire:click="resetCalculation"
                        >
                            Reset
                        </x-filament::button>

                        @if($calculation)
                            <x-filament::button
                                type="button"
                                color="success"
                                wire:click="saveEstimate"
                            >
                                <x-heroicon-m-document-plus class="w-5 h-5 mr-2" />
                                Save Estimate
                            </x-filament::button>
                        @endif
                    @endif
                </div>
            </form>
        </div>

        <!-- Results Section -->
        @if($showResults)
            <!-- Production Options (Auto Mode) -->
            @if($productionOptions && count($productionOptions) > 0)
                <div class="bg-white rounded-lg shadow">
                    <div class="px-6 py-4 border-b border-gray-200">
                        <h3 class="text-lg font-semibold text-gray-900">
                            <x-heroicon-m-cog-6-tooth class="w-5 h-5 inline mr-2" />
                            Production Options ({{ count($productionOptions) }} found)
                        </h3>
                    </div>

                    @if($selectedProductionOption)
                        <div class="px-6 py-3 bg-blue-50 border-b border-blue-200">
                            <div class="flex items-center justify-between">
                                <div class="text-sm text-blue-800">
                                    <strong>Selected:</strong> {{ $selectedProductionOption['machine']->name }} - {{ $selectedProductionOption['material_size']->name }}
                                    (£{{ number_format($selectedProductionOption['calculation']['costs']['final_price'], 2) }})
                                </div>
                                <x-filament::button
                                    type="button"
                                    color="success"
                                    size="sm"
                                    wire:click="saveEstimate"
                                >
                                    <x-heroicon-m-document-plus class="w-4 h-4 mr-1" />
                                    Save Estimate
                                </x-filament::button>
                            </div>
                        </div>
                    @endif

                    <div class="overflow-x-auto">
                        <table class="w-100% divide-y divide-gray-200">
                            <thead class="bg-gray-50">
                                <tr>
                                    <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Machine</th>
                                    <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Sheet Size</th>
                                    <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Layout</th>
                                    <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Sheets</th>
                                    <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Cost</th>
                                    <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Sell</th>
                                    <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Margin</th>
                                    <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                                </tr>
                            </thead>
                            <tbody class="bg-white divide-y divide-gray-200">
                                @foreach($productionOptions as $index => $option)
                                    <tr class="hover:bg-gray-50 {{ $selectedProductionOption && $selectedProductionOption['id'] === $option['id'] ? 'bg-blue-50' : '' }}">
                                        <td class="px-4 py-3 text-sm">
                                            <div class="font-medium text-gray-900">{{ $option['machine']->name }}</div>
                                            <div class="text-gray-500 text-xs">{{ $option['production_method']->name }}</div>
                                        </td>
                                        <td class="px-4 py-3 text-sm text-gray-900">
                                            {{ $option['material_size']->name }}
                                            <div class="text-gray-500 text-xs">{{ $option['material_size']->width_mm }}×{{ $option['material_size']->height_mm }}mm</div>
                                        </td>
                                        <td class="px-4 py-3 text-sm text-gray-900">
                                            {{ $option['calculation']['imposition']['items_per_sheet'] }}-up
                                            @if($option['calculation']['imposition']['rotated'])
                                                <span class="text-blue-600 text-xs">(Rotated)</span>
                                            @endif
                                            <div class="text-gray-500 text-xs">{{ $option['calculation']['imposition']['items_horizontal'] }}×{{ $option['calculation']['imposition']['items_vertical'] }}</div>
                                        </td>
                                        <td class="px-4 py-3 text-sm text-gray-900">{{ $option['sheets_required'] }}</td>
                                        <td class="px-4 py-3 text-sm text-gray-900">£{{ number_format($option['calculation']['costs']['total_cost'], 2) }}</td>
                                        <td class="px-4 py-3 text-sm font-medium text-gray-900">£{{ number_format($option['calculation']['costs']['final_price'], 2) }}</td>
                                        <td class="px-4 py-3 text-sm font-medium text-green-600">£{{ number_format($option['calculation']['costs']['final_price'] - $option['calculation']['costs']['total_cost'], 2) }}</td>
                                        <td class="px-4 py-3 text-sm">
                                            <div class="flex space-x-2">
                                                <button
                                                    wire:click="selectProductionOption('{{ $option['id'] }}')"
                                                    class="text-blue-600 hover:text-blue-900 text-xs font-medium"
                                                >
                                                    {{ $selectedProductionOption && $selectedProductionOption['id'] === $option['id'] ? 'Selected' : 'Select' }}
                                                </button>
                                                <button
                                                    wire:click="$toggle('showDetails.{{ $index }}')"
                                                    class="text-gray-600 hover:text-gray-900 text-xs font-medium"
                                                >
                                                    Details
                                                </button>
                                            </div>
                                        </td>
                                    </tr>

                                    <!-- Expandable Details Row -->
                                    @if($this->showDetails[$index] ?? false)
                                        <tr class="bg-gray-50">
                                            <td colspan="8" class="px-4 py-4">
                                                <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                                                    <!-- Cost Breakdown -->
                                                    <div>
                                                        <h4 class="text-sm font-medium text-gray-900 mb-3">Cost Breakdown</h4>
                                                        <div class="space-y-2 text-sm">
                                                            <div class="flex justify-between">
                                                                <span class="text-gray-600">Material Cost:</span>
                                                                <span>£{{ number_format($option['calculation']['costs']['material_cost'], 2) }}</span>
                                                            </div>
                                                            <div class="flex justify-between">
                                                                <span class="text-gray-600">Production Cost:</span>
                                                                <span>£{{ number_format($option['calculation']['costs']['production_cost'], 2) }}</span>
                                                            </div>
                                                            <div class="flex justify-between">
                                                                <span class="text-gray-600">Setup Cost:</span>
                                                                <span>£{{ number_format($option['calculation']['costs']['setup_cost'], 2) }}</span>
                                                            </div>
                                                            <div class="flex justify-between border-t pt-2 font-medium">
                                                                <span>Total Cost:</span>
                                                                <span>£{{ number_format($option['calculation']['costs']['total_cost'], 2) }}</span>
                                                            </div>
                                                            <div class="flex justify-between">
                                                                <span class="text-gray-600">Markup:</span>
                                                                <span>£{{ number_format($option['calculation']['costs']['final_price'] - $option['calculation']['costs']['total_cost'], 2) }}</span>
                                                            </div>
                                                            <div class="flex justify-between border-t pt-2 font-medium text-green-600">
                                                                <span>Sell Price:</span>
                                                                <span>£{{ number_format($option['calculation']['costs']['final_price'], 2) }}</span>
                                                            </div>
                                                        </div>
                                                    </div>

                                                    <!-- Imposition Details -->
                                                    <div>
                                                        <h4 class="text-sm font-medium text-gray-900 mb-3">Imposition Details</h4>
                                                        <div class="space-y-2 text-sm">
                                                            <div class="flex justify-between">
                                                                <span class="text-gray-600">Sheet Size:</span>
                                                                <span>{{ $option['calculation']['imposition']['layout_data']['sheet_width'] }}×{{ $option['calculation']['imposition']['layout_data']['sheet_height'] }}mm</span>
                                                            </div>
                                                            <div class="flex justify-between">
                                                                <span class="text-gray-600">Item Size:</span>
                                                                <span>{{ $option['calculation']['imposition']['layout_data']['item_width'] }}×{{ $option['calculation']['imposition']['layout_data']['item_height'] }}mm</span>
                                                            </div>
                                                            <div class="flex justify-between">
                                                                <span class="text-gray-600">Items per Sheet:</span>
                                                                <span>{{ $option['calculation']['imposition']['items_per_sheet'] }}</span>
                                                            </div>
                                                            <div class="flex justify-between">
                                                                <span class="text-gray-600">Sheets Required:</span>
                                                                <span>{{ $option['calculation']['imposition']['sheets_required'] }}</span>
                                                            </div>
                                                            <div class="flex justify-between">
                                                                <span class="text-gray-600">Efficiency:</span>
                                                                <span>{{ number_format($option['calculation']['imposition']['efficiency_percentage'], 1) }}%</span>
                                                            </div>
                                                            <div class="flex justify-between">
                                                                <span class="text-gray-600">Production Time:</span>
                                                                <span>{{ number_format($option['production_time'], 2) }} hours</span>
                                                            </div>
                                                        </div>

                                                        <!-- Visual Layout Preview -->
                                                        @if(isset($option['calculation']['imposition']['positions']))
                                                            <div class="mt-4">
                                                                <h5 class="text-xs font-medium text-gray-700 mb-2">Layout Preview</h5>
                                                                <div class="border rounded p-2 bg-white" style="max-width: 200px;">
                                                                    @include('filament.infolists.imposition-visualization-content', ['imposition' => $option['calculation']['imposition'], 'compact' => true])
                                                                </div>
                                                            </div>
                                                        @endif
                                                    </div>
                                                </div>
                                            </td>
                                        </tr>
                                    @endif
                                @endforeach
                            </tbody>
                        </table>
                    </div>
                </div>
            @endif


        @endif
    </div>
</x-filament-panels::page>
