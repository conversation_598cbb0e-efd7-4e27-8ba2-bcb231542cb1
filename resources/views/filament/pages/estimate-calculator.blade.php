<x-filament-panels::page>
    <div class="space-y-6">
        <!-- Form Section -->
        <div class="bg-white rounded-lg shadow p-6">
            <form wire:submit="calculate">
                {{ $this->form }}

                <div class="mt-6 flex gap-3">
                    <x-filament::button type="submit" size="lg">
                        <x-heroicon-m-calculator class="w-5 h-5 mr-2" />
                        Calculate Estimate
                    </x-filament::button>

                    @if($showResults)
                        <x-filament::button
                            type="button"
                            color="gray"
                            wire:click="resetCalculation"
                        >
                            Reset
                        </x-filament::button>

                        @if($calculation)
                            <x-filament::button
                                type="button"
                                color="success"
                                wire:click="saveEstimate"
                            >
                                <x-heroicon-m-document-plus class="w-5 h-5 mr-2" />
                                Save Estimate
                            </x-filament::button>
                        @endif
                    @endif
                </div>
            </form>
        </div>

        <!-- Results Section -->
        @if($showResults)
            <!-- Production Options (Auto Mode) -->
            @if($productionOptions && count($productionOptions) > 0)
                <div class="bg-white rounded-lg shadow p-6">
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">
                        <x-heroicon-m-cog-6-tooth class="w-5 h-5 inline mr-2" />
                        Production Options ({{ count($productionOptions) }} found)
                    </h3>

                    <div class="space-y-3">
                        @foreach($productionOptions as $option)
                            <div class="border rounded-lg p-4 cursor-pointer transition-colors
                                {{ $selectedProductionOption && $selectedProductionOption['id'] === $option['id']
                                   ? 'border-blue-500 bg-blue-50'
                                   : 'border-gray-200 hover:border-gray-300' }}"
                                 wire:click="selectProductionOption('{{ $option['id'] }}')">

                                <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
                                    <div>
                                        <h4 class="font-medium text-gray-900">{{ $option['machine']->name }}</h4>
                                        <p class="text-sm text-gray-600">{{ $option['material_size']->display_name }}</p>
                                    </div>

                                    <div>
                                        <p class="text-sm text-gray-600">
                                            {{ $option['calculation']['imposition']['items_per_sheet'] }}-up layout
                                            @if($option['calculation']['imposition']['rotated']) (Rotated) @endif
                                        </p>
                                        <p class="text-sm text-gray-600">{{ $option['calculation']['sheets_needed'] }} sheets</p>
                                    </div>

                                    <div>
                                        <p class="text-sm text-gray-600">Total: £{{ number_format($option['calculation']['costs']['total_cost'], 2) }}</p>
                                        <p class="text-sm text-gray-600">Sell: £{{ number_format($option['calculation']['costs']['final_price'], 2) }}</p>
                                    </div>

                                    <div>
                                        <p class="text-lg font-bold text-green-600">
                                            £{{ number_format($option['calculation']['costs']['gross_margin'], 2) }}
                                        </p>
                                        <p class="text-sm text-gray-600">Gross Margin</p>
                                    </div>
                                </div>
                            </div>
                        @endforeach
                    </div>
                </div>
            @endif

            <!-- Calculation Results & Visualization -->
            @if($calculation && $showVisualization)
                <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                    <!-- Cost Breakdown -->
                    <div class="bg-white rounded-lg shadow p-6">
                        <h3 class="text-lg font-semibold text-gray-900 mb-4">
                            <x-heroicon-m-currency-pound class="w-5 h-5 inline mr-2" />
                            Cost Breakdown
                        </h3>

                        <div class="space-y-3">
                            <div class="flex justify-between">
                                <span class="text-gray-600">Material Cost:</span>
                                <span class="font-medium">£{{ number_format($calculation['costs']['material_cost'], 2) }}</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-gray-600">Production Cost:</span>
                                <span class="font-medium">£{{ number_format($calculation['costs']['production_cost'], 2) }}</span>
                            </div>
                            <div class="flex justify-between border-t pt-2">
                                <span class="text-gray-600">Total Cost:</span>
                                <span class="font-medium">£{{ number_format($calculation['costs']['total_cost'], 2) }}</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-gray-600">Markup ({{ $calculation['costs']['markup_percentage'] }}%):</span>
                                <span class="font-medium">£{{ number_format($calculation['costs']['markup_amount'], 2) }}</span>
                            </div>
                            <div class="flex justify-between border-t pt-2 text-lg font-bold">
                                <span class="text-gray-900">Sell Price:</span>
                                <span class="text-green-600">£{{ number_format($calculation['costs']['final_price'], 2) }}</span>
                            </div>
                            <div class="flex justify-between text-lg font-bold">
                                <span class="text-gray-900">Gross Margin:</span>
                                <span class="text-blue-600">£{{ number_format($calculation['costs']['gross_margin'], 2) }}</span>
                            </div>
                        </div>
                    </div>

                    <!-- Imposition Details -->
                    <div class="bg-white rounded-lg shadow p-6">
                        <h3 class="text-lg font-semibold text-gray-900 mb-4">
                            <x-heroicon-m-squares-2x2 class="w-5 h-5 inline mr-2" />
                            Imposition Details
                        </h3>

                        @if($imposition)
                            <div class="space-y-3">
                                <div class="flex justify-between">
                                    <span class="text-gray-600">Sheet Size:</span>
                                    <span class="font-medium">{{ $imposition['sheet_width'] }}mm × {{ $imposition['sheet_height'] }}mm</span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-gray-600">Items per Sheet:</span>
                                    <span class="font-medium">{{ $imposition['items_per_sheet'] }} ({{ $imposition['items_across'] }} × {{ $imposition['items_down'] }})</span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-gray-600">Sheets Needed:</span>
                                    <span class="font-medium">{{ $calculation['sheets_needed'] }}</span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-gray-600">Waste Percentage:</span>
                                    <span class="font-medium">{{ number_format($imposition['waste_percentage'], 1) }}%</span>
                                </div>
                                @if($imposition['rotated'])
                                    <div class="flex justify-between">
                                        <span class="text-gray-600">Rotation:</span>
                                        <span class="font-medium text-blue-600">Items rotated for better fit</span>
                                    </div>
                                @endif
                            </div>
                        @endif
                    </div>
                </div>

                <!-- Visual Imposition -->
                @if($imposition && isset($imposition['positions']))
                    <div class="bg-white rounded-lg shadow p-6">
                        <h3 class="text-lg font-semibold text-gray-900 mb-4">
                            <x-heroicon-m-eye class="w-5 h-5 inline mr-2" />
                            Visual Layout
                        </h3>

                        @include('filament.infolists.imposition-visualization-content', ['imposition' => $imposition])
                    </div>
                @endif
            @endif
        @endif
    </div>
</x-filament-panels::page>
