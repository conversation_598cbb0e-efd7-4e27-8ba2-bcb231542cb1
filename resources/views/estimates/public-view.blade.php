<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Estimate {{ $estimate->estimate_number }} - {{ $estimate->job_title }}</title>
    <script src="https://cdn.tailwindcss.com"></script>
</head>
<body class="bg-gray-50 min-h-screen">
    <div class="max-w-4xl mx-auto py-8 px-4">
        <!-- Header -->
        <div class="bg-white rounded-lg shadow-sm border p-6 mb-6">
            <div class="flex justify-between items-start">
                <div>
                    <h1 class="text-3xl font-bold text-gray-900">Print Estimate</h1>
                    <p class="text-lg text-gray-600 mt-1">{{ $estimate->job_title }}</p>
                    <p class="text-sm text-gray-500">Estimate #{{ $estimate->estimate_number }}</p>
                </div>
                <div class="text-right">
                    <div class="text-3xl font-bold text-blue-600">£{{ number_format($estimate->final_price, 2) }}</div>
                    <div class="text-sm text-gray-500">£{{ number_format($estimate->final_price / $estimate->total_quantity, 4) }} per item</div>
                </div>
            </div>
        </div>

        <!-- Status Messages -->
        @if(session('success'))
            <div class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-6">
                {{ session('success') }}
            </div>
        @endif

        @if(session('error'))
            <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-6">
                {{ session('error') }}
            </div>
        @endif

        <!-- Status Badge -->
        @if($estimate->status === 'approved')
            <div class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-6">
                <div class="flex items-center">
                    <svg class="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                    </svg>
                    <strong>Estimate Approved</strong> - This estimate has been approved and a job has been created.
                </div>
            </div>
        @elseif($estimate->status === 'rejected')
            <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-6">
                <div class="flex items-center">
                    <svg class="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd"></path>
                    </svg>
                    <strong>Estimate Declined</strong> - This estimate has been declined.
                </div>
                @if($estimate->rejection_reason)
                    <p class="mt-2 text-sm">Reason: {{ $estimate->rejection_reason }}</p>
                @endif
            </div>
        @endif

        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <!-- Project Details -->
            <div class="bg-white rounded-lg shadow-sm border p-6">
                <h2 class="text-xl font-semibold text-gray-900 mb-4">Project Details</h2>
                
                <div class="space-y-3">
                    <div class="flex justify-between">
                        <span class="text-gray-600">Client:</span>
                        <span class="font-medium">{{ $estimate->client_name }}</span>
                    </div>
                    
                    @if($estimate->customer)
                    <div class="flex justify-between">
                        <span class="text-gray-600">Company:</span>
                        <span class="font-medium">{{ $estimate->customer->name }}</span>
                    </div>
                    @endif
                    
                    <div class="flex justify-between">
                        <span class="text-gray-600">Quantity:</span>
                        <span class="font-medium">{{ number_format($estimate->total_quantity) }} items</span>
                    </div>
                    
                    <div class="flex justify-between">
                        <span class="text-gray-600">Item Size:</span>
                        <span class="font-medium">{{ $estimate->item_width }}mm × {{ $estimate->item_height }}mm</span>
                    </div>
                    
                    @if($estimate->description)
                    <div class="pt-3 border-t">
                        <span class="text-gray-600">Description:</span>
                        <p class="mt-1 text-gray-900">{{ $estimate->description }}</p>
                    </div>
                    @endif
                </div>
            </div>

            <!-- Production Details -->
            <div class="bg-white rounded-lg shadow-sm border p-6">
                <h2 class="text-xl font-semibold text-gray-900 mb-4">Production Details</h2>
                
                <div class="space-y-3">
                    <div class="flex justify-between">
                        <span class="text-gray-600">Material:</span>
                        <span class="font-medium">{{ $estimate->material->display_name }}</span>
                    </div>
                    
                    @if($estimate->materialSize)
                    <div class="flex justify-between">
                        <span class="text-gray-600">Sheet Size:</span>
                        <span class="font-medium">{{ $estimate->materialSize->display_name }}</span>
                    </div>
                    @endif
                    
                    <div class="flex justify-between">
                        <span class="text-gray-600">Production Method:</span>
                        <span class="font-medium">{{ $estimate->productionMethod->name }}</span>
                    </div>
                    
                    <div class="flex justify-between">
                        <span class="text-gray-600">Machine:</span>
                        <span class="font-medium">{{ $estimate->machine->name }}</span>
                    </div>
                    
                    <div class="flex justify-between">
                        <span class="text-gray-600">Items per Sheet:</span>
                        <span class="font-medium">{{ $estimate->items_per_sheet }}</span>
                    </div>
                    
                    <div class="flex justify-between">
                        <span class="text-gray-600">Sheets Required:</span>
                        <span class="font-medium">{{ number_format($estimate->sheets_required) }}</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- Versions (if applicable) -->
        @if($estimate->has_versions && $estimate->versions)
        <div class="bg-white rounded-lg shadow-sm border p-6 mt-6">
            <h2 class="text-xl font-semibold text-gray-900 mb-4">Versions</h2>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                @foreach($estimate->versions as $version)
                <div class="bg-gray-50 rounded-lg p-4">
                    <h3 class="font-medium text-gray-900">{{ $version['name'] }}</h3>
                    <p class="text-sm text-gray-600">{{ number_format($version['quantity']) }} items</p>
                    <p class="text-xs text-gray-500">{{ number_format(($version['quantity'] / $estimate->total_quantity) * 100, 1) }}% of total</p>
                </div>
                @endforeach
            </div>
        </div>
        @endif

        <!-- Cost Breakdown -->
        <div class="bg-white rounded-lg shadow-sm border p-6 mt-6">
            <h2 class="text-xl font-semibold text-gray-900 mb-4">Cost Breakdown</h2>
            
            <div class="space-y-3">
                <div class="flex justify-between">
                    <span class="text-gray-600">Material Cost:</span>
                    <span class="font-medium">£{{ number_format($estimate->material_cost, 2) }}</span>
                </div>
                
                <div class="flex justify-between">
                    <span class="text-gray-600">Production Cost:</span>
                    <span class="font-medium">£{{ number_format($estimate->production_cost, 2) }}</span>
                </div>
                
                <div class="flex justify-between">
                    <span class="text-gray-600">Setup Cost:</span>
                    <span class="font-medium">£{{ number_format($estimate->setup_cost, 2) }}</span>
                </div>
                
                <div class="flex justify-between border-t pt-3">
                    <span class="text-gray-600">Subtotal:</span>
                    <span class="font-medium">£{{ number_format($estimate->total_cost, 2) }}</span>
                </div>
                
                <div class="flex justify-between">
                    <span class="text-gray-600">Markup ({{ $estimate->markup_percentage }}%):</span>
                    <span class="font-medium">£{{ number_format($estimate->final_price - $estimate->total_cost, 2) }}</span>
                </div>
                
                <div class="flex justify-between border-t pt-3 text-lg font-bold">
                    <span class="text-gray-900">Final Price:</span>
                    <span class="text-blue-600">£{{ number_format($estimate->final_price, 2) }}</span>
                </div>
            </div>
        </div>

        <!-- Actions -->
        @if($estimate->status === 'draft' || $estimate->status === 'sent')
        <div class="bg-white rounded-lg shadow-sm border p-6 mt-6">
            <h2 class="text-xl font-semibold text-gray-900 mb-4">Actions</h2>
            <p class="text-gray-600 mb-6">Please review the estimate details above and choose an action below:</p>
            
            <div class="flex flex-col sm:flex-row gap-4">
                <form action="{{ route('estimates.public.accept', $estimate->public_hash) }}" method="POST" class="flex-1">
                    @csrf
                    <button type="submit" class="w-full bg-green-600 hover:bg-green-700 text-white font-medium py-3 px-6 rounded-lg transition duration-200">
                        Accept Estimate
                    </button>
                </form>
                
                <button onclick="showDeclineForm()" class="flex-1 bg-red-600 hover:bg-red-700 text-white font-medium py-3 px-6 rounded-lg transition duration-200">
                    Decline Estimate
                </button>
            </div>
            
            <!-- Decline Form (hidden by default) -->
            <div id="declineForm" class="hidden mt-6 p-4 bg-gray-50 rounded-lg">
                <form action="{{ route('estimates.public.decline', $estimate->public_hash) }}" method="POST">
                    @csrf
                    <label for="reason" class="block text-sm font-medium text-gray-700 mb-2">
                        Reason for declining (optional):
                    </label>
                    <textarea name="reason" id="reason" rows="3" class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-red-500" placeholder="Please let us know why you're declining this estimate..."></textarea>
                    <div class="flex gap-3 mt-4">
                        <button type="submit" class="bg-red-600 hover:bg-red-700 text-white font-medium py-2 px-4 rounded transition duration-200">
                            Confirm Decline
                        </button>
                        <button type="button" onclick="hideDeclineForm()" class="bg-gray-300 hover:bg-gray-400 text-gray-700 font-medium py-2 px-4 rounded transition duration-200">
                            Cancel
                        </button>
                    </div>
                </form>
            </div>
        </div>
        @endif

        <!-- Footer -->
        <div class="text-center text-gray-500 text-sm mt-8">
            <p>This estimate is valid for 30 days from the date of issue.</p>
            <p>If you have any questions, please contact us.</p>
        </div>
    </div>

    <script>
        function showDeclineForm() {
            document.getElementById('declineForm').classList.remove('hidden');
        }
        
        function hideDeclineForm() {
            document.getElementById('declineForm').classList.add('hidden');
        }
    </script>
</body>
</html>
